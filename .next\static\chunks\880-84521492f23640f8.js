"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[880],{620:(e,t)=>{function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,a=o>>>1;r<a;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else if(c<o&&0>i(u,n))e[r]=u,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a,l=performance;t.unstable_now=function(){return l.now()}}else{var s=Date,c=s.now();t.unstable_now=function(){return s.now()-c}}var u=[],f=[],d=1,p=null,v=3,h=!1,b=!1,m=!1,y="function"==typeof setTimeout?setTimeout:null,g="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function j(e){for(var t=r(f);null!==t;){if(null===t.callback)o(f);else if(t.startTime<=e)o(f),t.sortIndex=t.expirationTime,n(u,t);else break;t=r(f)}}function x(e){if(m=!1,j(e),!b)if(null!==r(u))b=!0,O();else{var t=r(f);null!==t&&T(x,t.startTime-e)}}var C=!1,E=-1,_=5,k=-1;function P(){return!(t.unstable_now()-k<_)}function M(){if(C){var e=t.unstable_now();k=e;var n=!0;try{e:{b=!1,m&&(m=!1,g(E),E=-1),h=!0;var i=v;try{t:{for(j(e),p=r(u);null!==p&&!(p.expirationTime>e&&P());){var l=p.callback;if("function"==typeof l){p.callback=null,v=p.priorityLevel;var s=l(p.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){p.callback=s,j(e),n=!0;break t}p===r(u)&&o(u),j(e)}else o(u);p=r(u)}if(null!==p)n=!0;else{var c=r(f);null!==c&&T(x,c.startTime-e),n=!1}}break e}finally{p=null,v=i,h=!1}}}finally{n?a():C=!1}}}if("function"==typeof w)a=function(){w(M)};else if("undefined"!=typeof MessageChannel){var S=new MessageChannel,A=S.port2;S.port1.onmessage=M,a=function(){A.postMessage(null)}}else a=function(){y(M,0)};function O(){C||(C=!0,a())}function T(e,n){E=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){b||h||(b=!0,O())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return v},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(v){case 1:case 2:case 3:var t=3;break;default:t=v}var n=v;v=t;try{return e()}finally{v=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=v;v=e;try{return t()}finally{v=n}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=0x3fffffff;break;case 4:l=1e4;break;default:l=5e3}return l=i+l,e={id:d++,callback:o,priorityLevel:e,startTime:i,expirationTime:l,sortIndex:-1},i>a?(e.sortIndex=i,n(f,e),null===r(u)&&e===r(f)&&(m?(g(E),E=-1):m=!0,T(x,i-a))):(e.sortIndex=l,n(u,e),b||h||(b=!0,O())),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=v;return function(){var n=v;v=t;try{return e.apply(this,arguments)}finally{v=n}}}},1788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},1933:(e,t,n)=>{e.exports=n(6500)},2436:(e,t,n)=>{var r=n(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,s=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,u=r[1];return l(function(){o.value=n,o.getSnapshot=t,c(o)&&u({inst:o})},[e,n,t]),a(function(){return c(o)&&u({inst:o}),e(function(){c(o)&&u({inst:o})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},2894:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},3816:(e,t,n)=>{let r,o,i,a,l;n.d(t,{B:()=>q,C:()=>eu,E:()=>D,a:()=>H,b:()=>R,c:()=>eL,d:()=>eH,e:()=>ew,f:()=>eZ,i:()=>z,u:()=>F});var s=n(3264),c=n(7431),u=n(2115),f=n.t(u,2),d=n(1933),p=n(5643);let v=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,o,i);return i},h=e=>e?v(e):v,{useSyncExternalStoreWithSelector:b}=p,m=e=>e,y=(e,t)=>{let n=h(e),r=(e,r=t)=>(function(e,t=m,n){let r=b(e.subscribe,e.getState,e.getInitialState,t,n);return u.useDebugValue(r),r})(n,e,r);return Object.assign(r,n),r},g=(e,t)=>e?y(e,t):y;var w=n(5220),j=n.n(w),x=n(4342);let C=e=>"object"==typeof e&&"function"==typeof e.then,E=[];function _(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;let r=e.length;if(t.length!==r)return!1;for(let o=0;o<r;o++)if(!n(e[o],t[o]))return!1;return!0}function k(e,t=null,n=!1,r={}){for(let o of(null===t&&(t=[e]),E))if(_(t,o.keys,o.equal)){if(n)return;if(Object.prototype.hasOwnProperty.call(o,"error"))throw o.error;if(Object.prototype.hasOwnProperty.call(o,"response"))return r.lifespan&&r.lifespan>0&&(o.timeout&&clearTimeout(o.timeout),o.timeout=setTimeout(o.remove,r.lifespan)),o.response;if(!n)throw o.promise}let o={keys:t,equal:r.equal,remove:()=>{let e=E.indexOf(o);-1!==e&&E.splice(e,1)},promise:(C(e)?e:e(...t)).then(e=>{o.response=e,r.lifespan&&r.lifespan>0&&(o.timeout=setTimeout(o.remove,r.lifespan))}).catch(e=>o.error=e)};if(E.push(o),!n)throw o.promise}let P=(e,t,n)=>k(e,t,!1,n),M=(e,t,n)=>void k(e,t,!0,n),S=e=>{if(void 0===e||0===e.length)E.splice(0,E.length);else{let t=E.find(t=>_(e,t.keys,t.equal));t&&t.remove()}};var A=n(5155),O=n(6354);function T(e){let t=e.root;for(;t.getState().previousRoot;)t=t.getState().previousRoot;return t}n(9509),f.act;let I=e=>e&&e.isOrthographicCamera,z=e=>e&&e.hasOwnProperty("current"),L=e=>null!=e&&("string"==typeof e||"number"==typeof e||e.isColor),R=((e,t)=>"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative"))()?u.useLayoutEffect:u.useEffect;function H(e){let t=u.useRef(e);return R(()=>void(t.current=e),[e]),t}function F(){let e=(0,O.u5)(),t=(0,O.y3)();return u.useMemo(()=>({children:n})=>{let r=(0,O.Nz)(e,!0,e=>e.type===u.StrictMode)?u.StrictMode:u.Fragment;return(0,A.jsx)(r,{children:(0,A.jsx)(t,{children:n})})},[e,t])}function q({set:e}){return R(()=>(e(new Promise(()=>null)),()=>e(!1)),[e]),null}let D=(e=>((e=class extends u.Component{constructor(...e){super(...e),this.state={error:!1}}componentDidCatch(e){this.props.set(e)}render(){return this.state.error?null:this.props.children}}).getDerivedStateFromError=()=>({error:!0}),e))();function U(e){var t;let n="undefined"!=typeof window?null!=(t=window.devicePixelRatio)?t:2:1;return Array.isArray(e)?Math.min(Math.max(e[0],n),e[1]):e}function $(e){var t;return null==(t=e.__r3f)?void 0:t.root.getState()}let N={obj:e=>e===Object(e)&&!N.arr(e)&&"function"!=typeof e,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,boo:e=>"boolean"==typeof e,und:e=>void 0===e,nul:e=>null===e,arr:e=>Array.isArray(e),equ(e,t,{arrays:n="shallow",objects:r="reference",strict:o=!0}={}){let i;if(typeof e!=typeof t||!!e!=!!t)return!1;if(N.str(e)||N.num(e)||N.boo(e))return e===t;let a=N.obj(e);if(a&&"reference"===r)return e===t;let l=N.arr(e);if(l&&"reference"===n)return e===t;if((l||a)&&e===t)return!0;for(i in e)if(!(i in t))return!1;if(a&&"shallow"===n&&"shallow"===r){for(i in o?t:e)if(!N.equ(e[i],t[i],{strict:o,objects:"reference"}))return!1}else for(i in o?t:e)if(e[i]!==t[i])return!1;if(N.und(i)){if(l&&0===e.length&&0===t.length||a&&0===Object.keys(e).length&&0===Object.keys(t).length)return!0;if(e!==t)return!1}return!0}},V=["children","key","ref"];function W(e,t,n,r){let o=null==e?void 0:e.__r3f;return!o&&(o={root:t,type:n,parent:null,children:[],props:function(e){let t={};for(let n in e)V.includes(n)||(t[n]=e[n]);return t}(r),object:e,eventCount:0,handlers:{},isHidden:!1},e&&(e.__r3f=o)),o}function B(e,t){let n=e[t];if(!t.includes("-"))return{root:e,key:t,target:n};for(let o of(n=e,t.split("-"))){var r;t=o,e=n,n=null==(r=n)?void 0:r[t]}return{root:e,key:t,target:n}}let Y=/-\d+$/;function G(e,t){if(N.str(t.props.attach)){if(Y.test(t.props.attach)){let n=t.props.attach.replace(Y,""),{root:r,key:o}=B(e.object,n);Array.isArray(r[o])||(r[o]=[])}let{root:n,key:r}=B(e.object,t.props.attach);t.previousAttach=n[r],n[r]=t.object}else N.fun(t.props.attach)&&(t.previousAttach=t.props.attach(e.object,t.object))}function X(e,t){if(N.str(t.props.attach)){let{root:n,key:r}=B(e.object,t.props.attach),o=t.previousAttach;void 0===o?delete n[r]:n[r]=o}else null==t.previousAttach||t.previousAttach(e.object,t.object);delete t.previousAttach}let Z=[...V,"args","dispose","attach","object","onUpdate","dispose"],K=new Map,Q=["map","emissiveMap","sheenColorMap","specularColorMap","envMap"],J=/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;function ee(e,t){var n,r;let o=e.__r3f,i=o&&T(o).getState(),a=null==o?void 0:o.eventCount;for(let n in t){let a=t[n];if(Z.includes(n))continue;if(o&&J.test(n)){"function"==typeof a?o.handlers[n]=a:delete o.handlers[n],o.eventCount=Object.keys(o.handlers).length;continue}if(void 0===a)continue;let{root:l,key:c,target:u}=B(e,n);u instanceof s.zgK&&a instanceof s.zgK?u.mask=a.mask:u instanceof s.Q1f&&L(a)?u.set(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"function"==typeof u.copy&&null!=a&&a.constructor&&u.constructor===a.constructor?u.copy(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&Array.isArray(a)?"function"==typeof u.fromArray?u.fromArray(a):u.set(...a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"number"==typeof a?"function"==typeof u.setScalar?u.setScalar(a):u.set(a):(l[c]=a,i&&!i.linear&&Q.includes(c)&&null!=(r=l[c])&&r.isTexture&&l[c].format===s.GWd&&l[c].type===s.OUM&&(l[c].colorSpace=s.er$))}if(null!=o&&o.parent&&null!=i&&i.internal&&null!=(n=o.object)&&n.isObject3D&&a!==o.eventCount){let e=o.object,t=i.internal.interaction.indexOf(e);t>-1&&i.internal.interaction.splice(t,1),o.eventCount&&null!==e.raycast&&i.internal.interaction.push(e)}return o&&void 0===o.props.attach&&(o.object.isBufferGeometry?o.props.attach="geometry":o.object.isMaterial&&(o.props.attach="material")),o&&et(o),e}function et(e){var t;if(!e.parent)return;null==e.props.onUpdate||e.props.onUpdate(e.object);let n=null==(t=e.root)||null==t.getState?void 0:t.getState();n&&0===n.internal.frames&&n.invalidate()}function en(e,t){e.manual||(I(e)?(e.left=-(t.width/2),e.right=t.width/2,e.top=t.height/2,e.bottom=-(t.height/2)):e.aspect=t.width/t.height,e.updateProjectionMatrix())}let er=e=>null==e?void 0:e.isObject3D;function eo(e){return(e.eventObject||e.object).uuid+"/"+e.index+e.instanceId}function ei(e,t,n,r){let o=n.get(t);o&&(n.delete(t),0===n.size&&(e.delete(r),o.target.releasePointerCapture(r)))}let ea=e=>!!(null!=e&&e.render),el=u.createContext(null),es=(e,t)=>{let n=g((n,r)=>{let o,i=new s.Pq0,a=new s.Pq0,l=new s.Pq0;function c(e=r().camera,t=a,n=r().size){let{width:o,height:s,top:u,left:f}=n,d=o/s;t.isVector3?l.copy(t):l.set(...t);let p=e.getWorldPosition(i).distanceTo(l);if(I(e))return{width:o/e.zoom,height:s/e.zoom,top:u,left:f,factor:1,distance:p,aspect:d};{let t=2*Math.tan(e.fov*Math.PI/180/2)*p,n=o/s*t;return{width:n,height:t,top:u,left:f,factor:o/n,distance:p,aspect:d}}}let f=e=>n(t=>({performance:{...t.performance,current:e}})),d=new s.I9Y;return{set:n,get:r,gl:null,camera:null,raycaster:null,events:{priority:1,enabled:!0,connected:!1},scene:null,xr:null,invalidate:(t=1)=>e(r(),t),advance:(e,n)=>t(e,n,r()),legacy:!1,linear:!1,flat:!1,controls:null,clock:new s.zD7,pointer:d,mouse:d,frameloop:"always",onPointerMissed:void 0,performance:{current:1,min:.5,max:1,debounce:200,regress:()=>{let e=r();o&&clearTimeout(o),e.performance.current!==e.performance.min&&f(e.performance.min),o=setTimeout(()=>f(r().performance.max),e.performance.debounce)}},size:{width:0,height:0,top:0,left:0},viewport:{initialDpr:0,dpr:0,width:0,height:0,top:0,left:0,aspect:0,distance:0,factor:0,getCurrentViewport:c},setEvents:e=>n(t=>({...t,events:{...t.events,...e}})),setSize:(e,t,o=0,i=0)=>{let l=r().camera,s={width:e,height:t,top:o,left:i};n(e=>({size:s,viewport:{...e.viewport,...c(l,a,s)}}))},setDpr:e=>n(t=>{let n=U(e);return{viewport:{...t.viewport,dpr:n,initialDpr:t.viewport.initialDpr||n}}}),setFrameloop:(e="always")=>{let t=r().clock;t.stop(),t.elapsedTime=0,"never"!==e&&(t.start(),t.elapsedTime=0),n(()=>({frameloop:e}))},previousRoot:void 0,internal:{interaction:[],hovered:new Map,subscribers:[],initialClick:[0,0],initialHits:[],capturedMap:new Map,lastEvent:u.createRef(),active:!1,frames:0,priority:0,subscribe:(e,t,n)=>{let o=r().internal;return o.priority=o.priority+ +(t>0),o.subscribers.push({ref:e,priority:t,store:n}),o.subscribers=o.subscribers.sort((e,t)=>e.priority-t.priority),()=>{let n=r().internal;null!=n&&n.subscribers&&(n.priority=n.priority-(t>0),n.subscribers=n.subscribers.filter(t=>t.ref!==e))}}}}}),r=n.getState(),o=r.size,i=r.viewport.dpr,a=r.camera;return n.subscribe(()=>{let{camera:e,size:t,viewport:r,gl:l,set:s}=n.getState();if(t.width!==o.width||t.height!==o.height||r.dpr!==i){o=t,i=r.dpr,en(e,t),r.dpr>0&&l.setPixelRatio(r.dpr);let n="undefined"!=typeof HTMLCanvasElement&&l.domElement instanceof HTMLCanvasElement;l.setSize(t.width,t.height,n)}e!==a&&(a=e,s(t=>({viewport:{...t.viewport,...t.viewport.getCurrentViewport(e)}})))}),n.subscribe(t=>e(t)),n};function ec(){let e=u.useContext(el);if(!e)throw Error("R3F: Hooks can only be used within the Canvas component!");return e}function eu(e,t=0){let n=ec(),r=n.getState().internal.subscribe,o=H(e);return R(()=>r(o,t,n),[t,r,n]),null}let ef=new WeakMap,ed=e=>{var t;return"function"==typeof e&&(null==e||null==(t=e.prototype)?void 0:t.constructor)===e};function ep(e,t){return function(n,...r){let o;return ed(n)?(o=ef.get(n))||(o=new n,ef.set(n,o)):o=n,e&&e(o),Promise.all(r.map(e=>new Promise((n,r)=>o.load(e,e=>{er(null==e?void 0:e.scene)&&Object.assign(e,function(e){let t={nodes:{},materials:{},meshes:{}};return e&&e.traverse(e=>{e.name&&(t.nodes[e.name]=e),e.material&&!t.materials[e.material.name]&&(t.materials[e.material.name]=e.material),e.isMesh&&!t.meshes[e.name]&&(t.meshes[e.name]=e)}),t}(e.scene)),n(e)},t,t=>r(Error(`Could not load ${e}: ${null==t?void 0:t.message}`))))))}}function ev(e,t,n,r){let o=Array.isArray(t)?t:[t],i=P(ep(n,r),[e,...o],{equal:N.equ});return Array.isArray(t)?i:i[0]}ev.preload=function(e,t,n){let r=Array.isArray(t)?t:[t];return M(ep(n),[e,...r])},ev.clear=function(e,t){return S([e,...Array.isArray(t)?t:[t]])};let eh={},eb=/^three(?=[A-Z])/,em=e=>`${e[0].toUpperCase()}${e.slice(1)}`,ey=0,eg=e=>"function"==typeof e;function ew(e){if(eg(e)){let t=`${ey++}`;return eh[t]=e,t}Object.assign(eh,e)}function ej(e,t){let n=em(e),r=eh[n];if("primitive"!==e&&!r)throw Error(`R3F: ${n} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);if("primitive"===e&&!t.object)throw Error("R3F: Primitives without 'object' are invalid!");if(void 0!==t.args&&!Array.isArray(t.args))throw Error("R3F: The args prop must be an array!")}function ex(e){if(e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?G(e.parent,e):er(e.object)&&!1!==e.props.visible&&(e.object.visible=!0),e.isHidden=!1,et(e)}}function eC(e,t,n){let r=t.root.getState();if(e.parent||e.object===r.scene){if(!t.object){var o,i;let e=eh[em(t.type)];t.object=null!=(o=t.props.object)?o:new e(...null!=(i=t.props.args)?i:[]),t.object.__r3f=t}if(ee(t.object,t.props),t.props.attach)G(e,t);else if(er(t.object)&&er(e.object)){let r=e.object.children.indexOf(null==n?void 0:n.object);if(n&&-1!==r){let n=e.object.children.indexOf(t.object);-1!==n?(e.object.children.splice(n,1),e.object.children.splice(n<r?r-1:r,0,t.object)):(t.object.parent=e.object,e.object.children.splice(r,0,t.object),t.object.dispatchEvent({type:"added"}),e.object.dispatchEvent({type:"childadded",child:t.object}))}else e.object.add(t.object)}for(let e of t.children)eC(t,e);et(t)}}function eE(e,t){t&&(t.parent=e,e.children.push(t),eC(e,t))}function e_(e,t,n){if(!t||!n)return;t.parent=e;let r=e.children.indexOf(n);-1!==r?e.children.splice(r,0,t):e.children.push(t),eC(e,t,n)}function ek(e){if("function"==typeof e.dispose){let t=()=>{try{e.dispose()}catch{}};"undefined"!=typeof IS_REACT_ACT_ENVIRONMENT?t():(0,x.unstable_scheduleCallback)(x.unstable_IdlePriority,t)}}function eP(e,t,n){if(!t)return;t.parent=null;let r=e.children.indexOf(t);-1!==r&&e.children.splice(r,1),t.props.attach?X(e,t):er(t.object)&&er(e.object)&&(e.object.remove(t.object),function(e,t){let{internal:n}=e.getState();n.interaction=n.interaction.filter(e=>e!==t),n.initialHits=n.initialHits.filter(e=>e!==t),n.hovered.forEach((e,r)=>{(e.eventObject===t||e.object===t)&&n.hovered.delete(r)}),n.capturedMap.forEach((e,r)=>{ei(n.capturedMap,t,e,r)})}(T(t),t.object));let o=null!==t.props.dispose&&!1!==n;for(let e=t.children.length-1;e>=0;e--){let n=t.children[e];eP(t,n,o)}t.children.length=0,delete t.object.__r3f,o&&"primitive"!==t.type&&"Scene"!==t.object.type&&ek(t.object),void 0===n&&et(t)}let eM=[],eS=()=>{},eA={},eO=0,eT=function(e){let t=j()(e);return t.injectIntoDevTools({bundleType:0,rendererPackageName:"@react-three/fiber",version:u.version}),t}({isPrimaryRenderer:!1,warnsIfNotActing:!1,supportsMutation:!0,supportsPersistence:!1,supportsHydration:!1,createInstance:function(e,t,n){var r;return ej(e=em(e)in eh?e:e.replace(eb,""),t),"primitive"===e&&null!=(r=t.object)&&r.__r3f&&delete t.object.__r3f,W(t.object,n,e,t)},removeChild:eP,appendChild:eE,appendInitialChild:eE,insertBefore:e_,appendChildToContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&eE(n,t)},removeChildFromContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&eP(n,t)},insertInContainerBefore(e,t,n){let r=e.getState().scene.__r3f;t&&n&&r&&e_(r,t,n)},getRootHostContext:()=>eA,getChildHostContext:()=>eA,commitUpdate(e,t,n,r,o){var i,a,l;ej(t,r);let s=!1;if("primitive"===e.type&&n.object!==r.object||(null==(i=r.args)?void 0:i.length)!==(null==(a=n.args)?void 0:a.length)?s=!0:null!=(l=r.args)&&l.some((e,t)=>{var r;return e!==(null==(r=n.args)?void 0:r[t])})&&(s=!0),s)eM.push([e,{...r},o]);else{let t=function(e,t){let n={};for(let r in t)if(!Z.includes(r)&&!N.equ(t[r],e.props[r]))for(let e in n[r]=t[r],t)e.startsWith(`${r}-`)&&(n[e]=t[e]);for(let r in e.props){if(Z.includes(r)||t.hasOwnProperty(r))continue;let{root:o,key:i}=B(e.object,r);if(o.constructor&&0===o.constructor.length){let e=function(e){let t=K.get(e.constructor);try{t||(t=new e.constructor,K.set(e.constructor,t))}catch(e){}return t}(o);N.und(e)||(n[i]=e[i])}else n[i]=0}return n}(e,r);Object.keys(t).length&&(Object.assign(e.props,t),ee(e.object,t))}(null===o.sibling||(4&o.flags)==0)&&function(){for(let[e]of eM){let t=e.parent;if(t)for(let n of(e.props.attach?X(t,e):er(e.object)&&er(t.object)&&t.object.remove(e.object),e.children))n.props.attach?X(e,n):er(n.object)&&er(e.object)&&e.object.remove(n.object);e.isHidden&&ex(e),e.object.__r3f&&delete e.object.__r3f,"primitive"!==e.type&&ek(e.object)}for(let[r,o,i]of eM){r.props=o;let a=r.parent;if(a){let o=eh[em(r.type)];r.object=null!=(e=r.props.object)?e:new o(...null!=(t=r.props.args)?t:[]),r.object.__r3f=r;var e,t,n=r.object;for(let e of[i,i.alternate])if(null!==e)if("function"==typeof e.ref){null==e.refCleanup||e.refCleanup();let t=e.ref(n);"function"==typeof t&&(e.refCleanup=t)}else e.ref&&(e.ref.current=n);for(let e of(ee(r.object,r.props),r.props.attach?G(a,r):er(r.object)&&er(a.object)&&a.object.add(r.object),r.children))e.props.attach?G(r,e):er(e.object)&&er(r.object)&&r.object.add(e.object);et(r)}}eM.length=0}()},finalizeInitialChildren:()=>!1,commitMount(){},getPublicInstance:e=>null==e?void 0:e.object,prepareForCommit:()=>null,preparePortalMount:e=>W(e.getState().scene,e,"",{}),resetAfterCommit:()=>{},shouldSetTextContent:()=>!1,clearContainer:()=>!1,hideInstance:function(e){if(!e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?X(e.parent,e):er(e.object)&&(e.object.visible=!1),e.isHidden=!0,et(e)}},unhideInstance:ex,createTextInstance:eS,hideTextInstance:eS,unhideTextInstance:eS,scheduleTimeout:"function"==typeof setTimeout?setTimeout:void 0,cancelTimeout:"function"==typeof clearTimeout?clearTimeout:void 0,noTimeout:-1,getInstanceFromNode:()=>null,beforeActiveInstanceBlur(){},afterActiveInstanceBlur(){},detachDeletedInstance(){},prepareScopeUpdate(){},getInstanceFromScope:()=>null,shouldAttemptEagerTransition:()=>!1,trackSchedulerEvent:()=>{},resolveEventType:()=>null,resolveEventTimeStamp:()=>-1.1,requestPostPaintCallback(){},maySuspendCommit:()=>!1,preloadInstance:()=>!0,startSuspendingCommit(){},suspendInstance(){},waitForCommitToBeReady:()=>null,NotPendingTransition:null,HostTransitionContext:u.createContext(null),setCurrentUpdatePriority(e){eO=e},getCurrentUpdatePriority:()=>eO,resolveUpdatePriority(){var e;if(0!==eO)return eO;switch("undefined"!=typeof window&&(null==(e=window.event)?void 0:e.type)){case"click":case"contextmenu":case"dblclick":case"pointercancel":case"pointerdown":case"pointerup":return d.DiscreteEventPriority;case"pointermove":case"pointerout":case"pointerover":case"pointerenter":case"pointerleave":case"wheel":return d.ContinuousEventPriority;default:return d.DefaultEventPriority}},resetFormInstance(){}}),eI=new Map,ez={objects:"shallow",strict:!1};function eL(e){let t,n,r=eI.get(e),o=null==r?void 0:r.fiber,i=null==r?void 0:r.store;r&&console.warn("R3F.createRoot should only be called once!");let a="function"==typeof reportError?reportError:console.error,l=i||es(eY,eG),u=o||eT.createContainer(l,d.ConcurrentRoot,null,!1,null,"",a,a,a,null);r||eI.set(e,{fiber:u,store:l});let f=!1,p=null;return{async configure(r={}){var o,i;let a;p=new Promise(e=>a=e);let{gl:u,size:d,scene:v,events:h,onCreated:b,shadows:m=!1,linear:y=!1,flat:g=!1,legacy:w=!1,orthographic:j=!1,frameloop:x="always",dpr:C=[1,2],performance:E,raycaster:_,camera:k,onPointerMissed:P}=r,M=l.getState(),S=M.gl;if(!M.gl){let t={canvas:e,powerPreference:"high-performance",antialias:!0,alpha:!0},n="function"==typeof u?await u(t):u;S=ea(n)?n:new c.WebGLRenderer({...t,...u}),M.set({gl:S})}let A=M.raycaster;A||M.set({raycaster:A=new s.tBo});let{params:O,...T}=_||{};if(N.equ(T,A,ez)||ee(A,{...T}),N.equ(O,A.params,ez)||ee(A,{params:{...A.params,...O}}),!M.camera||M.camera===n&&!N.equ(n,k,ez)){n=k;let e=null==k?void 0:k.isCamera,t=e?k:j?new s.qUd(0,0,0,0,.1,1e3):new s.ubm(75,0,.1,1e3);!e&&(t.position.z=5,k&&(ee(t,k),!t.manual&&("aspect"in k||"left"in k||"right"in k||"bottom"in k||"top"in k)&&(t.manual=!0,t.updateProjectionMatrix())),M.camera||null!=k&&k.rotation||t.lookAt(0,0,0)),M.set({camera:t}),A.camera=t}if(!M.scene){let e;null!=v&&v.isScene?W(e=v,l,"",{}):(W(e=new s.Z58,l,"",{}),v&&ee(e,v)),M.set({scene:e})}h&&!M.events.handlers&&M.set({events:h(l)});let I=function(e,t){if(!t&&"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&e.parentElement){let{width:t,height:n,top:r,left:o}=e.parentElement.getBoundingClientRect();return{width:t,height:n,top:r,left:o}}return!t&&"undefined"!=typeof OffscreenCanvas&&e instanceof OffscreenCanvas?{width:e.width,height:e.height,top:0,left:0}:{width:0,height:0,top:0,left:0,...t}}(e,d);if(N.equ(I,M.size,ez)||M.setSize(I.width,I.height,I.top,I.left),C&&M.viewport.dpr!==U(C)&&M.setDpr(C),M.frameloop!==x&&M.setFrameloop(x),M.onPointerMissed||M.set({onPointerMissed:P}),E&&!N.equ(E,M.performance,ez)&&M.set(e=>({performance:{...e.performance,...E}})),!M.xr){let e=(e,t)=>{let n=l.getState();"never"!==n.frameloop&&eG(e,!0,n,t)},t=()=>{let t=l.getState();t.gl.xr.enabled=t.gl.xr.isPresenting,t.gl.xr.setAnimationLoop(t.gl.xr.isPresenting?e:null),t.gl.xr.isPresenting||eY(t)},n={connect(){let e=l.getState().gl;e.xr.addEventListener("sessionstart",t),e.xr.addEventListener("sessionend",t)},disconnect(){let e=l.getState().gl;e.xr.removeEventListener("sessionstart",t),e.xr.removeEventListener("sessionend",t)}};"function"==typeof(null==(o=S.xr)?void 0:o.addEventListener)&&n.connect(),M.set({xr:n})}if(S.shadowMap){let e=S.shadowMap.enabled,t=S.shadowMap.type;if(S.shadowMap.enabled=!!m,N.boo(m))S.shadowMap.type=s.Wk7;else if(N.str(m)){let e={basic:s.bTm,percentage:s.QP0,soft:s.Wk7,variance:s.RyA};S.shadowMap.type=null!=(i=e[m])?i:s.Wk7}else N.obj(m)&&Object.assign(S.shadowMap,m);(e!==S.shadowMap.enabled||t!==S.shadowMap.type)&&(S.shadowMap.needsUpdate=!0)}return s.ppV.enabled=!w,f||(S.outputColorSpace=y?s.Zr2:s.er$,S.toneMapping=g?s.y_p:s.FV),M.legacy!==w&&M.set(()=>({legacy:w})),M.linear!==y&&M.set(()=>({linear:y})),M.flat!==g&&M.set(()=>({flat:g})),!u||N.fun(u)||ea(u)||N.equ(u,S,ez)||ee(S,u),t=b,f=!0,a(),this},render(n){return f||p||this.configure(),p.then(()=>{eT.updateContainer((0,A.jsx)(eR,{store:l,children:n,onCreated:t,rootElement:e}),u,null,()=>void 0)}),l},unmount(){eH(e)}}}function eR({store:e,children:t,onCreated:n,rootElement:r}){return R(()=>{let t=e.getState();t.set(e=>({internal:{...e.internal,active:!0}})),n&&n(t),e.getState().events.connected||null==t.events.connect||t.events.connect(r)},[]),(0,A.jsx)(el.Provider,{value:e,children:t})}function eH(e,t){let n=eI.get(e),r=null==n?void 0:n.fiber;if(r){let o=null==n?void 0:n.store.getState();o&&(o.internal.active=!1),eT.updateContainer(null,r,null,()=>{o&&setTimeout(()=>{try{null==o.events.disconnect||o.events.disconnect(),null==(n=o.gl)||null==(r=n.renderLists)||null==r.dispose||r.dispose(),null==(i=o.gl)||null==i.forceContextLoss||i.forceContextLoss(),null!=(a=o.gl)&&a.xr&&o.xr.disconnect();var n,r,i,a,l=o.scene;for(let e in"Scene"!==l.type&&(null==l.dispose||l.dispose()),l){let t=l[e];(null==t?void 0:t.type)!=="Scene"&&(null==t||null==t.dispose||t.dispose())}eI.delete(e),t&&t(e)}catch(e){}},500)})}}let eF=new Set,eq=new Set,eD=new Set;function eU(e,t){if(e.size)for(let{callback:n}of e.values())n(t)}function e$(e,t){switch(e){case"before":return eU(eF,t);case"after":return eU(eq,t);case"tail":return eU(eD,t)}}function eN(e,t,n){let i=t.clock.getDelta();"never"===t.frameloop&&"number"==typeof e&&(i=e-t.clock.elapsedTime,t.clock.oldTime=t.clock.elapsedTime,t.clock.elapsedTime=e),r=t.internal.subscribers;for(let e=0;e<r.length;e++)(o=r[e]).ref.current(o.store.getState(),i,n);return!t.internal.priority&&t.gl.render&&t.gl.render(t.scene,t.camera),t.internal.frames=Math.max(0,t.internal.frames-1),"always"===t.frameloop?1:t.internal.frames}let eV=!1,eW=!1;function eB(e){for(let n of(a=requestAnimationFrame(eB),eV=!0,i=0,e$("before",e),eW=!0,eI.values())){var t;(l=n.store.getState()).internal.active&&("always"===l.frameloop||l.internal.frames>0)&&!(null!=(t=l.gl.xr)&&t.isPresenting)&&(i+=eN(e,l))}if(eW=!1,e$("after",e),0===i)return e$("tail",e),eV=!1,cancelAnimationFrame(a)}function eY(e,t=1){var n;if(!e)return eI.forEach(e=>eY(e.store.getState(),t));(null==(n=e.gl.xr)||!n.isPresenting)&&e.internal.active&&"never"!==e.frameloop&&(t>1?e.internal.frames=Math.min(60,e.internal.frames+t):eW?e.internal.frames=2:e.internal.frames=1,eV||(eV=!0,requestAnimationFrame(eB)))}function eG(e,t=!0,n,r){if(t&&e$("before",e),n)eN(e,n,r);else for(let t of eI.values())eN(e,t.store.getState());t&&e$("after",e)}let eX={onClick:["click",!1],onContextMenu:["contextmenu",!1],onDoubleClick:["dblclick",!1],onWheel:["wheel",!0],onPointerDown:["pointerdown",!0],onPointerUp:["pointerup",!0],onPointerLeave:["pointerleave",!0],onPointerMove:["pointermove",!0],onPointerCancel:["pointercancel",!0],onLostPointerCapture:["lostpointercapture",!0]};function eZ(e){let{handlePointer:t}=function(e){function t(e){return e.filter(e=>["Move","Over","Enter","Out","Leave"].some(t=>{var n;return null==(n=e.__r3f)?void 0:n.handlers["onPointer"+t]}))}function n(t){let{internal:n}=e.getState();for(let e of n.hovered.values())if(!t.length||!t.find(t=>t.object===e.object&&t.index===e.index&&t.instanceId===e.instanceId)){let r=e.eventObject.__r3f;if(n.hovered.delete(eo(e)),null!=r&&r.eventCount){let n=r.handlers,o={...e,intersections:t};null==n.onPointerOut||n.onPointerOut(o),null==n.onPointerLeave||n.onPointerLeave(o)}}}function r(e,t){for(let n=0;n<t.length;n++){let r=t[n].__r3f;null==r||null==r.handlers.onPointerMissed||r.handlers.onPointerMissed(e)}}return{handlePointer:function(o){switch(o){case"onPointerLeave":case"onPointerCancel":return()=>n([]);case"onLostPointerCapture":return t=>{let{internal:r}=e.getState();"pointerId"in t&&r.capturedMap.has(t.pointerId)&&requestAnimationFrame(()=>{r.capturedMap.has(t.pointerId)&&(r.capturedMap.delete(t.pointerId),n([]))})}}return function(i){let{onPointerMissed:a,internal:l}=e.getState();l.lastEvent.current=i;let c="onPointerMove"===o,u="onClick"===o||"onContextMenu"===o||"onDoubleClick"===o,f=function(t,n){let r=e.getState(),o=new Set,i=[],a=n?n(r.internal.interaction):r.internal.interaction;for(let e=0;e<a.length;e++){let t=$(a[e]);t&&(t.raycaster.camera=void 0)}r.previousRoot||null==r.events.compute||r.events.compute(t,r);let l=a.flatMap(function(e){let n=$(e);if(!n||!n.events.enabled||null===n.raycaster.camera)return[];if(void 0===n.raycaster.camera){var r;null==n.events.compute||n.events.compute(t,n,null==(r=n.previousRoot)?void 0:r.getState()),void 0===n.raycaster.camera&&(n.raycaster.camera=null)}return n.raycaster.camera?n.raycaster.intersectObject(e,!0):[]}).sort((e,t)=>{let n=$(e.object),r=$(t.object);return n&&r&&r.events.priority-n.events.priority||e.distance-t.distance}).filter(e=>{let t=eo(e);return!o.has(t)&&(o.add(t),!0)});for(let e of(r.events.filter&&(l=r.events.filter(l,r)),l)){let t=e.object;for(;t;){var s;null!=(s=t.__r3f)&&s.eventCount&&i.push({...e,eventObject:t}),t=t.parent}}if("pointerId"in t&&r.internal.capturedMap.has(t.pointerId))for(let e of r.internal.capturedMap.get(t.pointerId).values())o.has(eo(e.intersection))||i.push(e.intersection);return i}(i,c?t:void 0),d=u?function(t){let{internal:n}=e.getState(),r=t.offsetX-n.initialClick[0],o=t.offsetY-n.initialClick[1];return Math.round(Math.sqrt(r*r+o*o))}(i):0;"onPointerDown"===o&&(l.initialClick=[i.offsetX,i.offsetY],l.initialHits=f.map(e=>e.eventObject)),u&&!f.length&&d<=2&&(r(i,l.interaction),a&&a(i)),c&&n(f),!function(e,t,r,o){if(e.length){let i={stopped:!1};for(let a of e){let l=$(a.object);if(l||a.object.traverseAncestors(e=>{let t=$(e);if(t)return l=t,!1}),l){let{raycaster:c,pointer:u,camera:f,internal:d}=l,p=new s.Pq0(u.x,u.y,0).unproject(f),v=e=>{var t,n;return null!=(t=null==(n=d.capturedMap.get(e))?void 0:n.has(a.eventObject))&&t},h=e=>{let n={intersection:a,target:t.target};d.capturedMap.has(e)?d.capturedMap.get(e).set(a.eventObject,n):d.capturedMap.set(e,new Map([[a.eventObject,n]])),t.target.setPointerCapture(e)},b=e=>{let t=d.capturedMap.get(e);t&&ei(d.capturedMap,a.eventObject,t,e)},m={};for(let e in t){let n=t[e];"function"!=typeof n&&(m[e]=n)}let y={...a,...m,pointer:u,intersections:e,stopped:i.stopped,delta:r,unprojectedPoint:p,ray:c.ray,camera:f,stopPropagation(){let r="pointerId"in t&&d.capturedMap.get(t.pointerId);(!r||r.has(a.eventObject))&&(y.stopped=i.stopped=!0,d.hovered.size&&Array.from(d.hovered.values()).find(e=>e.eventObject===a.eventObject)&&n([...e.slice(0,e.indexOf(a)),a]))},target:{hasPointerCapture:v,setPointerCapture:h,releasePointerCapture:b},currentTarget:{hasPointerCapture:v,setPointerCapture:h,releasePointerCapture:b},nativeEvent:t};if(o(y),!0===i.stopped)break}}}}(f,i,d,function(e){let t=e.eventObject,n=t.__r3f;if(!(null!=n&&n.eventCount))return;let a=n.handlers;if(c){if(a.onPointerOver||a.onPointerEnter||a.onPointerOut||a.onPointerLeave){let t=eo(e),n=l.hovered.get(t);n?n.stopped&&e.stopPropagation():(l.hovered.set(t,e),null==a.onPointerOver||a.onPointerOver(e),null==a.onPointerEnter||a.onPointerEnter(e))}null==a.onPointerMove||a.onPointerMove(e)}else{let n=a[o];n?(!u||l.initialHits.includes(t))&&(r(i,l.interaction.filter(e=>!l.initialHits.includes(e))),n(e)):u&&l.initialHits.includes(t)&&r(i,l.interaction.filter(e=>!l.initialHits.includes(e)))}})}}}}(e);return{priority:1,enabled:!0,compute(e,t,n){t.pointer.set(e.offsetX/t.size.width*2-1,-(2*(e.offsetY/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)},connected:void 0,handlers:Object.keys(eX).reduce((e,n)=>({...e,[n]:t(n)}),{}),update:()=>{var t;let{events:n,internal:r}=e.getState();null!=(t=r.lastEvent)&&t.current&&n.handlers&&n.handlers.onPointerMove(r.lastEvent.current)},connect:t=>{let{set:n,events:r}=e.getState();if(null==r.disconnect||r.disconnect(),n(e=>({events:{...e.events,connected:t}})),r.handlers)for(let e in r.handlers){let n=r.handlers[e],[o,i]=eX[e];t.addEventListener(o,n,{passive:i})}},disconnect:()=>{let{set:t,events:n}=e.getState();if(n.connected){if(n.handlers)for(let e in n.handlers){let t=n.handlers[e],[r]=eX[e];n.connected.removeEventListener(r,t)}t(e=>({events:{...e.events,connected:void 0}}))}}}}},4342:(e,t,n)=>{e.exports=n(7319)},5220:(e,t,n)=>{e.exports=n(1724)},5643:(e,t,n)=>{e.exports=n(6115)},5830:(e,t,n)=>{let r,o;n.d(t,{ON:()=>v});var i=n(9630),a=n(3264),l=n(2115),s=n(3816);a.YJl;let c=l.createContext(null),u=new a.kn4,f=new a.Pq0,d=l.forwardRef(({children:e,range:t,limit:n=1e3,...d},p)=>{let v=l.useRef(null);l.useImperativeHandle(p,()=>v.current,[]);let[h,b]=l.useState([]),[[m,y,g]]=l.useState(()=>[new Float32Array(3*n),Float32Array.from({length:3*n},()=>1),Float32Array.from({length:n},()=>1)]);l.useEffect(()=>{v.current.geometry.attributes.position.needsUpdate=!0}),(0,s.C)(()=>{for(v.current.updateMatrix(),v.current.updateMatrixWorld(),u.copy(v.current.matrixWorld).invert(),v.current.geometry.drawRange.count=Math.min(n,void 0!==t?t:n,h.length),r=0;r<h.length;r++)(o=h[r].current).getWorldPosition(f).applyMatrix4(u),f.toArray(m,3*r),v.current.geometry.attributes.position.needsUpdate=!0,o.matrixWorldNeedsUpdate=!0,o.color.toArray(y,3*r),v.current.geometry.attributes.color.needsUpdate=!0,g.set([o.size],r),v.current.geometry.attributes.size.needsUpdate=!0});let w=l.useMemo(()=>({getParent:()=>v,subscribe:e=>(b(t=>[...t,e]),()=>b(t=>t.filter(t=>t.current!==e.current)))}),[]);return l.createElement("points",(0,i.A)({userData:{instances:h},matrixAutoUpdate:!1,ref:v,raycast:()=>null},d),l.createElement("bufferGeometry",null,l.createElement("bufferAttribute",{attach:"attributes-position",args:[m,3],usage:a.Vnu}),l.createElement("bufferAttribute",{attach:"attributes-color",args:[y,3],usage:a.Vnu}),l.createElement("bufferAttribute",{attach:"attributes-size",args:[g,1],usage:a.Vnu})),l.createElement(c.Provider,{value:w},e))}),p=l.forwardRef(({children:e,positions:t,colors:n,sizes:r,stride:o=3,...c},u)=>{let f=l.useRef(null);return l.useImperativeHandle(u,()=>f.current,[]),(0,s.C)(()=>{let e=f.current.geometry.attributes;e.position.needsUpdate=!0,n&&(e.color.needsUpdate=!0),r&&(e.size.needsUpdate=!0)}),l.createElement("points",(0,i.A)({ref:f},c),l.createElement("bufferGeometry",null,l.createElement("bufferAttribute",{attach:"attributes-position",args:[t,o],usage:a.Vnu}),n&&l.createElement("bufferAttribute",{attach:"attributes-color",args:[n,o],count:n.length/o,usage:a.Vnu}),r&&l.createElement("bufferAttribute",{attach:"attributes-size",args:[r,1],count:r.length/o,usage:a.Vnu})),e)}),v=l.forwardRef((e,t)=>e.positions instanceof Float32Array?l.createElement(p,(0,i.A)({},e,{ref:t})):l.createElement(d,(0,i.A)({},e,{ref:t})))},6115:(e,t,n)=>{var r=n(2115),o=n(9033),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,l=r.useRef,s=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=a(e,(f=c(function(){function e(e){if(!s){if(s=!0,a=e,e=r(e),void 0!==o&&d.hasValue){var t=d.value;if(o(t,e))return l=t}return l=e}if(t=l,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,l=n)}var a,l,s=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,n,r,o]))[0],f[1]);return s(function(){d.hasValue=!0,d.value=p},[p]),u(p),p}},6354:(e,t,n)=>{n.d(t,{Af:()=>l,Nz:()=>o,u5:()=>s,y3:()=>f});var r=n(2115);function o(e,t,n){if(!e)return;if(!0===n(e))return e;let r=t?e.return:e.child;for(;r;){let e=o(r,t,n);if(e)return e;r=t?null:r.sibling}}function i(e){try{return Object.defineProperties(e,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(t){return e}}(()=>{var e,t;return"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative")})()?r.useLayoutEffect:r.useEffect;let a=i(r.createContext(null));class l extends r.Component{render(){return r.createElement(a.Provider,{value:this._reactInternals},this.props.children)}}function s(){let e=r.useContext(a);if(null===e)throw Error("its-fine: useFiber must be called within a <FiberProvider />!");let t=r.useId();return r.useMemo(()=>{for(let n of[e,null==e?void 0:e.alternate]){if(!n)continue;let e=o(n,!1,e=>{let n=e.memoizedState;for(;n;){if(n.memoizedState===t)return!0;n=n.next}});if(e)return e}},[e,t])}let c=Symbol.for("react.context"),u=e=>null!==e&&"object"==typeof e&&"$$typeof"in e&&e.$$typeof===c;function f(){let e=function(){let e=s(),[t]=r.useState(()=>new Map);t.clear();let n=e;for(;n;){let e=n.type;u(e)&&e!==a&&!t.has(e)&&t.set(e,r.use(i(e))),n=n.return}return t}();return r.useMemo(()=>Array.from(e.keys()).reduce((t,n)=>o=>r.createElement(t,null,r.createElement(n.Provider,{...o,value:e.get(n)})),e=>r.createElement(l,{...e})),[e])}},6500:(e,t)=>{t.ConcurrentRoot=1,t.ContinuousEventPriority=8,t.DefaultEventPriority=32,t.DiscreteEventPriority=2},7319:(e,t)=>{function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,a=o>>>1;r<a;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else if(c<o&&0>i(u,n))e[r]=u,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a,l=performance;t.unstable_now=function(){return l.now()}}else{var s=Date,c=s.now();t.unstable_now=function(){return s.now()-c}}var u=[],f=[],d=1,p=null,v=3,h=!1,b=!1,m=!1,y="function"==typeof setTimeout?setTimeout:null,g="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function j(e){for(var t=r(f);null!==t;){if(null===t.callback)o(f);else if(t.startTime<=e)o(f),t.sortIndex=t.expirationTime,n(u,t);else break;t=r(f)}}function x(e){if(m=!1,j(e),!b)if(null!==r(u))b=!0,O();else{var t=r(f);null!==t&&T(x,t.startTime-e)}}var C=!1,E=-1,_=5,k=-1;function P(){return!(t.unstable_now()-k<_)}function M(){if(C){var e=t.unstable_now();k=e;var n=!0;try{e:{b=!1,m&&(m=!1,g(E),E=-1),h=!0;var i=v;try{t:{for(j(e),p=r(u);null!==p&&!(p.expirationTime>e&&P());){var l=p.callback;if("function"==typeof l){p.callback=null,v=p.priorityLevel;var s=l(p.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){p.callback=s,j(e),n=!0;break t}p===r(u)&&o(u),j(e)}else o(u);p=r(u)}if(null!==p)n=!0;else{var c=r(f);null!==c&&T(x,c.startTime-e),n=!1}}break e}finally{p=null,v=i,h=!1}}}finally{n?a():C=!1}}}if("function"==typeof w)a=function(){w(M)};else if("undefined"!=typeof MessageChannel){var S=new MessageChannel,A=S.port2;S.port1.onmessage=M,a=function(){A.postMessage(null)}}else a=function(){y(M,0)};function O(){C||(C=!0,a())}function T(e,n){E=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){b||h||(b=!0,O())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return v},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(v){case 1:case 2:case 3:var t=3;break;default:t=v}var n=v;v=t;try{return e()}finally{v=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=v;v=e;try{return t()}finally{v=n}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=0x3fffffff;break;case 4:l=1e4;break;default:l=5e3}return l=i+l,e={id:d++,callback:o,priorityLevel:e,startTime:i,expirationTime:l,sortIndex:-1},i>a?(e.sortIndex=i,n(f,e),null===r(u)&&e===r(f)&&(m?(g(E),E=-1):m=!0,T(x,i-a))):(e.sortIndex=l,n(u,e),b||h||(b=!0,O())),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=v;return function(){var n=v;v=t;try{return e.apply(this,arguments)}finally{v=n}}}},7558:(e,t,n)=>{n.d(t,{Hl:()=>d});var r=n(3816),o=n(2115),i=n(7431);function a(e,t){let n;return(...r)=>{window.clearTimeout(n),n=window.setTimeout(()=>e(...r),t)}}let l=["x","y","top","bottom","left","right","width","height"],s=(e,t)=>l.every(n=>e[n]===t[n]);var c=n(6354),u=n(5155);function f({ref:e,children:t,fallback:n,resize:l,style:c,gl:f,events:d=r.f,eventSource:p,eventPrefix:v,shadows:h,linear:b,flat:m,legacy:y,orthographic:g,frameloop:w,dpr:j,performance:x,raycaster:C,camera:E,scene:_,onPointerMissed:k,onCreated:P,...M}){o.useMemo(()=>(0,r.e)(i),[]);let S=(0,r.u)(),[A,O]=function({debounce:e,scroll:t,polyfill:n,offsetSize:r}={debounce:0,scroll:!1,offsetSize:!1}){var i,l,c;let u=n||("undefined"==typeof window?class{}:window.ResizeObserver);if(!u)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[f,d]=(0,o.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),p=(0,o.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:f,orientationHandler:null}),v=e?"number"==typeof e?e:e.scroll:null,h=e?"number"==typeof e?e:e.resize:null,b=(0,o.useRef)(!1);(0,o.useEffect)(()=>(b.current=!0,()=>void(b.current=!1)));let[m,y,g]=(0,o.useMemo)(()=>{let e=()=>{if(!p.current.element)return;let{left:e,top:t,width:n,height:o,bottom:i,right:a,x:l,y:c}=p.current.element.getBoundingClientRect(),u={left:e,top:t,width:n,height:o,bottom:i,right:a,x:l,y:c};p.current.element instanceof HTMLElement&&r&&(u.height=p.current.element.offsetHeight,u.width=p.current.element.offsetWidth),Object.freeze(u),b.current&&!s(p.current.lastBounds,u)&&d(p.current.lastBounds=u)};return[e,h?a(e,h):e,v?a(e,v):e]},[d,r,v,h]);function w(){p.current.scrollContainers&&(p.current.scrollContainers.forEach(e=>e.removeEventListener("scroll",g,!0)),p.current.scrollContainers=null),p.current.resizeObserver&&(p.current.resizeObserver.disconnect(),p.current.resizeObserver=null),p.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",p.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",p.current.orientationHandler))}function j(){p.current.element&&(p.current.resizeObserver=new u(g),p.current.resizeObserver.observe(p.current.element),t&&p.current.scrollContainers&&p.current.scrollContainers.forEach(e=>e.addEventListener("scroll",g,{capture:!0,passive:!0})),p.current.orientationHandler=()=>{g()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",p.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",p.current.orientationHandler))}return i=g,l=!!t,(0,o.useEffect)(()=>{if(l)return window.addEventListener("scroll",i,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",i,!0)},[i,l]),c=y,(0,o.useEffect)(()=>(window.addEventListener("resize",c),()=>void window.removeEventListener("resize",c)),[c]),(0,o.useEffect)(()=>{w(),j()},[t,g,y]),(0,o.useEffect)(()=>w,[]),[e=>{e&&e!==p.current.element&&(w(),p.current.element=e,p.current.scrollContainers=function e(t){let n=[];if(!t||t===document.body)return n;let{overflow:r,overflowX:o,overflowY:i}=window.getComputedStyle(t);return[r,o,i].some(e=>"auto"===e||"scroll"===e)&&n.push(t),[...n,...e(t.parentElement)]}(e),j())},f,m]}({scroll:!0,debounce:{scroll:50,resize:0},...l}),T=o.useRef(null),I=o.useRef(null);o.useImperativeHandle(e,()=>T.current);let z=(0,r.a)(k),[L,R]=o.useState(!1),[H,F]=o.useState(!1);if(L)throw L;if(H)throw H;let q=o.useRef(null);(0,r.b)(()=>{let e=T.current;O.width>0&&O.height>0&&e&&(q.current||(q.current=(0,r.c)(e)),async function(){await q.current.configure({gl:f,scene:_,events:d,shadows:h,linear:b,flat:m,legacy:y,orthographic:g,frameloop:w,dpr:j,performance:x,raycaster:C,camera:E,size:O,onPointerMissed:(...e)=>null==z.current?void 0:z.current(...e),onCreated:e=>{null==e.events.connect||e.events.connect(p?(0,r.i)(p)?p.current:p:I.current),v&&e.setEvents({compute:(e,t)=>{let n=e[v+"X"],r=e[v+"Y"];t.pointer.set(n/t.size.width*2-1,-(2*(r/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),null==P||P(e)}}),q.current.render((0,u.jsx)(S,{children:(0,u.jsx)(r.E,{set:F,children:(0,u.jsx)(o.Suspense,{fallback:(0,u.jsx)(r.B,{set:R}),children:null!=t?t:null})})}))}())}),o.useEffect(()=>{let e=T.current;if(e)return()=>(0,r.d)(e)},[]);let D=p?"none":"auto";return(0,u.jsx)("div",{ref:I,style:{position:"relative",width:"100%",height:"100%",overflow:"hidden",pointerEvents:D,...c},...M,children:(0,u.jsx)("div",{ref:A,style:{width:"100%",height:"100%"},children:(0,u.jsx)("canvas",{ref:T,style:{display:"block"},children:n})})})}function d(e){return(0,u.jsx)(c.Af,{children:(0,u.jsx)(f,{...e})})}n(1933),n(5220),n(4342)},8247:(e,t,n)=>{e.exports=n(620)},8832:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},8883:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9033:(e,t,n)=>{e.exports=n(2436)},9099:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9107:(e,t,n)=>{n.d(t,{q:()=>c});var r=n(9630),o=n(3264),i=n(2115);let a=parseInt(o.sPf.replace(/\D+/g,"")),l=a>=154?"opaque_fragment":"output_fragment";class s extends o.BH${constructor(e){super(e),this.onBeforeCompile=(e,t)=>{let{isWebGL2:n}=t.capabilities;e.fragmentShader=e.fragmentShader.replace(`#include <${l}>`,`
        ${!n?`#extension GL_OES_standard_derivatives : enable
#include <${l}>`:`#include <${l}>`}
      vec2 cxy = 2.0 * gl_PointCoord - 1.0;
      float r = dot(cxy, cxy);
      float delta = fwidth(r);     
      float mask = 1.0 - smoothstep(1.0 - delta, 1.0 + delta, r);
      gl_FragColor = vec4(gl_FragColor.rgb, mask * gl_FragColor.a );
      #include <tonemapping_fragment>
      #include <${a>=154?"colorspace_fragment":"encodings_fragment"}>
      `)}}}let c=i.forwardRef((e,t)=>{let[n]=i.useState(()=>new s(null));return i.createElement("primitive",(0,r.A)({},e,{object:n,ref:t,attach:"material"}))})},9630:(e,t,n)=>{n.d(t,{A:()=>r});function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}}}]);