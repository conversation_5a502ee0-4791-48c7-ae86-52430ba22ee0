"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Download, Mail, Phone, MapPin, Linkedin, Github } from "lucide-react";

const experience = [
  {
    title: "Frontend Developer Intern",
    company: "Tech Solutions Inc.",
    period: "Jun 2024 - Aug 2024",
    description: [
      "Developed responsive web applications using React and TypeScript",
      "Collaborated with design team to implement pixel-perfect UI components",
      "Optimized application performance resulting in 30% faster load times",
      "Participated in code reviews and agile development processes"
    ]
  },
  {
    title: "Web Development Freelancer",
    company: "Self-Employed",
    period: "Jan 2023 - Present",
    description: [
      "Built custom websites for small businesses using modern web technologies",
      "Implemented responsive designs and ensured cross-browser compatibility",
      "Integrated third-party APIs and payment gateways",
      "Provided ongoing maintenance and technical support"
    ]
  }
];

const projects = [
  {
    title: "E-Commerce Platform",
    technologies: ["React", "Node.js", "MongoDB"],
    description: "Full-stack application with user authentication and payment integration"
  },
  {
    title: "Task Management App",
    technologies: ["Next.js", "TypeScript", "PostgreSQL"],
    description: "Collaborative platform with real-time updates and team features"
  },
  {
    title: "AI Chat Assistant",
    technologies: ["Python", "TensorFlow", "Flask"],
    description: "Intelligent chatbot with natural language processing capabilities"
  }
];

const skills = {
  "Programming Languages": ["C++", "Java", "Python", "JavaScript", "TypeScript"],
  "Frontend": ["React", "Next.js", "HTML5", "CSS3", "Tailwind CSS"],
  "Backend": ["Node.js", "Express.js", "Flask", "REST APIs"],
  "Databases": ["MongoDB", "PostgreSQL", "SQL"],
  "Tools & Technologies": ["Git", "GitHub", "Selenium", "Docker", "Vercel"]
};

export default function Resume() {
  const downloadResume = () => {
    // In a real application, this would download the actual PDF
    const link = document.createElement('a');
    link.href = '/resume/janvi-kalwani-resume.pdf';
    link.download = 'Janvi_Kalwani_Resume.pdf';
    link.click();
  };

  return (
    <div className="pt-16 min-h-screen">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto"
        >
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl sm:text-5xl font-bold mb-4">
              <span className="gradient-text">Resume</span>
            </h1>
            <Button onClick={downloadResume} size="lg" className="animate-pulse-glow">
              <Download className="mr-2 h-4 w-4" />
              Download PDF
            </Button>
          </div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-8"
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-center">Janvi Kalwani</CardTitle>
                <p className="text-center text-muted-foreground">Aspiring Software Developer</p>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Mail className="h-4 w-4" />
                    <EMAIL>
                  </div>
                  <div className="flex items-center gap-1">
                    <Phone className="h-4 w-4" />
                    +91 98765 43210
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    Raipur, India
                  </div>
                  <div className="flex items-center gap-1">
                    <Linkedin className="h-4 w-4" />
                    linkedin.com/in/janvi-kalwani
                  </div>
                  <div className="flex items-center gap-1">
                    <Github className="h-4 w-4" />
                    github.com/janvi-kalwani
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Education */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-8"
          >
            <h2 className="text-2xl font-bold mb-4">Education</h2>
            <div className="space-y-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-semibold">Master of Computer Applications (MCA)</h3>
                      <p className="text-muted-foreground">Amity University Raipur</p>
                    </div>
                    <span className="text-sm text-muted-foreground">2022 - 2024</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-semibold">Bachelor of Computer Applications (BCA)</h3>
                      <p className="text-muted-foreground">ABVV Bilaspur</p>
                    </div>
                    <span className="text-sm text-muted-foreground">2019 - 2022</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </motion.div>

          {/* Experience */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mb-8"
          >
            <h2 className="text-2xl font-bold mb-4">Experience</h2>
            <div className="space-y-4">
              {experience.map((exp, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-semibold">{exp.title}</h3>
                        <p className="text-muted-foreground">{exp.company}</p>
                      </div>
                      <span className="text-sm text-muted-foreground">{exp.period}</span>
                    </div>
                    <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                      {exp.description.map((item, i) => (
                        <li key={i}>{item}</li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>

          {/* Skills */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mb-8"
          >
            <h2 className="text-2xl font-bold mb-4">Skills</h2>
            <div className="space-y-4">
              {Object.entries(skills).map(([category, skillList]) => (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="text-lg">{category}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {skillList.map((skill) => (
                        <Badge key={skill} variant="secondary">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>

          {/* Projects */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <h2 className="text-2xl font-bold mb-4">Key Projects</h2>
            <div className="space-y-4">
              {projects.map((project, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="mb-2">
                      <h3 className="font-semibold">{project.title}</h3>
                      <p className="text-sm text-muted-foreground mb-2">{project.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {project.technologies.map((tech) => (
                          <Badge key={tech} variant="outline" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
