(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var r=a(5155);a(2115);var s=a(4624),n=a(2085),i=a(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:n,asChild:l=!1,...c}=e,d=l?s.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:n,className:t})),...c})}},6565:(e,t,a)=>{Promise.resolve().then(a.bind(a,7569))},7569:(e,t,a)=>{"use strict";a.d(t,{HeroSection:()=>N});var r=a(5155),s=a(6408),n=a(285),i=a(1788),o=a(9099),l=a(2894),c=a(8883),d=a(8832),h=a(2115),m=a(3816),u=a(7558),x=a(5830),v=a(9107),g=a(3264);function p(){let e=(0,h.useRef)(null),[t,a]=(0,h.useMemo)(()=>{let e=new Float32Array(15e3),t=new Float32Array(15e3);for(let a=0;a<5e3;a++){let r=2*Math.random(),s=Math.random()*Math.PI*2,n=Math.acos(2*Math.random()-1);e[3*a]=r*Math.sin(n)*Math.cos(s),e[3*a+1]=r*Math.sin(n)*Math.sin(s),e[3*a+2]=r*Math.cos(n),t[3*a]=.5*Math.random()+.5,t[3*a+1]=.3*Math.random()+.4,t[3*a+2]=.3*Math.random()+.7}return[e,t]},[]);return(0,m.C)(t=>{e.current&&(e.current.rotation.x=.1*Math.sin(.1*t.clock.elapsedTime),e.current.rotation.y=.05*t.clock.elapsedTime,e.current.rotation.z=.05*Math.sin(.1*t.clock.elapsedTime))}),(0,r.jsx)(x.ON,{ref:e,positions:t,stride:3,frustumCulled:!1,children:(0,r.jsx)(v.q,{transparent:!0,vertexColors:!0,size:.015,sizeAttenuation:!0,depthWrite:!1,blending:g.EZo})})}function f(){let e=(0,h.useRef)(null);(0,m.C)(t=>{e.current&&(e.current.rotation.x=.1*Math.sin(.2*t.clock.elapsedTime),e.current.rotation.y=.1*t.clock.elapsedTime)});let t=(0,h.useMemo)(()=>{let e=[];for(let t=0;t<50;t++)e.push({position:[(Math.random()-.5)*10,(Math.random()-.5)*10,(Math.random()-.5)*10],rotation:[Math.random()*Math.PI,Math.random()*Math.PI,Math.random()*Math.PI],scale:.1*Math.random()+.05});return e},[]);return(0,r.jsx)("group",{ref:e,children:t.map((e,t)=>(0,r.jsxs)("mesh",{position:e.position,rotation:e.rotation,scale:e.scale,children:[(0,r.jsx)("boxGeometry",{args:[1,1,1]}),(0,r.jsx)("meshBasicMaterial",{color:new g.Q1f().setHSL(.6+.2*Math.random(),.7,.6),transparent:!0,opacity:.6,wireframe:!0})]},t))})}function b(){return(0,r.jsx)("div",{className:"absolute inset-0 -z-10",children:(0,r.jsxs)(u.Hl,{camera:{position:[0,0,5],fov:75},style:{background:"transparent"},children:[(0,r.jsx)("ambientLight",{intensity:.5}),(0,r.jsx)("pointLight",{position:[10,10,10]}),(0,r.jsx)(p,{}),(0,r.jsx)(f,{})]})})}var j=a(6874),w=a.n(j);let k={hidden:{opacity:0},visible:{opacity:1,transition:{delayChildren:.3,staggerChildren:.2}}},y={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.5}}};function N(){return(0,r.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,r.jsx)(b,{}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-background/90 via-background/70 to-background/90"}),(0,r.jsxs)(s.P.div,{variants:k,initial:"hidden",animate:"visible",className:"relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto",children:[(0,r.jsx)(s.P.div,{variants:y,className:"mb-6",children:(0,r.jsxs)("h1",{className:"text-4xl sm:text-6xl lg:text-7xl font-bold tracking-tight",children:[(0,r.jsx)("span",{className:"block",children:"Hi, I'm"}),(0,r.jsx)("span",{className:"block gradient-text",children:"Janvi Kalwani"})]})}),(0,r.jsx)(s.P.div,{variants:y,className:"mb-6",children:(0,r.jsx)("h2",{className:"text-xl sm:text-2xl lg:text-3xl font-medium text-muted-foreground",children:"Aspiring Software Developer"})}),(0,r.jsx)(s.P.div,{variants:y,className:"mb-8",children:(0,r.jsx)("p",{className:"text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed",children:"Building secure, scalable web and AI applications with modern technologies. Passionate about creating innovative solutions that make a difference."})}),(0,r.jsxs)(s.P.div,{variants:y,className:"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12",children:[(0,r.jsx)(w(),{href:"/projects",children:(0,r.jsx)(n.$,{size:"lg",className:"w-full sm:w-auto animate-pulse-glow",children:"View Projects"})}),(0,r.jsx)(w(),{href:"/resume",children:(0,r.jsxs)(n.$,{variant:"outline",size:"lg",className:"w-full sm:w-auto",children:[(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Download Resume"]})})]}),(0,r.jsxs)(s.P.div,{variants:y,className:"flex justify-center space-x-6 mb-12",children:[(0,r.jsx)(w(),{href:"https://github.com/janvi-kalwani",target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground hover:text-primary transition-colors",children:(0,r.jsx)(o.A,{className:"h-6 w-6"})}),(0,r.jsx)(w(),{href:"https://linkedin.com/in/janvi-kalwani",target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground hover:text-primary transition-colors",children:(0,r.jsx)(l.A,{className:"h-6 w-6"})}),(0,r.jsx)(w(),{href:"mailto:<EMAIL>",className:"text-muted-foreground hover:text-primary transition-colors",children:(0,r.jsx)(c.A,{className:"h-6 w-6"})})]}),(0,r.jsx)(s.P.div,{variants:y,className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,r.jsx)(n.$,{variant:"ghost",size:"icon",onClick:()=>{let e=document.getElementById("about");null==e||e.scrollIntoView({behavior:"smooth"})},className:"animate-bounce",children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})})]})]})}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var r=a(2596),s=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[367,831,413,154,874,880,441,684,358],()=>t(6565)),_N_E=e.O()}]);