[{"D:\\portfolio\\janvi-portfolio\\src\\app\\about\\page.tsx": "1", "D:\\portfolio\\janvi-portfolio\\src\\app\\contact\\page.tsx": "2", "D:\\portfolio\\janvi-portfolio\\src\\app\\layout.tsx": "3", "D:\\portfolio\\janvi-portfolio\\src\\app\\page.tsx": "4", "D:\\portfolio\\janvi-portfolio\\src\\app\\projects\\page.tsx": "5", "D:\\portfolio\\janvi-portfolio\\src\\app\\resume\\page.tsx": "6", "D:\\portfolio\\janvi-portfolio\\src\\components\\hero-section.tsx": "7", "D:\\portfolio\\janvi-portfolio\\src\\components\\navigation.tsx": "8", "D:\\portfolio\\janvi-portfolio\\src\\components\\theme-provider.tsx": "9", "D:\\portfolio\\janvi-portfolio\\src\\components\\three-background.tsx": "10", "D:\\portfolio\\janvi-portfolio\\src\\components\\ui\\badge.tsx": "11", "D:\\portfolio\\janvi-portfolio\\src\\components\\ui\\button.tsx": "12", "D:\\portfolio\\janvi-portfolio\\src\\components\\ui\\card.tsx": "13", "D:\\portfolio\\janvi-portfolio\\src\\components\\ui\\input.tsx": "14", "D:\\portfolio\\janvi-portfolio\\src\\components\\ui\\textarea.tsx": "15", "D:\\portfolio\\janvi-portfolio\\src\\lib\\utils.ts": "16"}, {"size": 6553, "mtime": 1748874845132, "results": "17", "hashOfConfig": "18"}, {"size": 8823, "mtime": 1748874923220, "results": "19", "hashOfConfig": "18"}, {"size": 1967, "mtime": 1748874711105, "results": "20", "hashOfConfig": "18"}, {"size": 170, "mtime": 1748874816037, "results": "21", "hashOfConfig": "18"}, {"size": 6559, "mtime": 1748874868193, "results": "22", "hashOfConfig": "18"}, {"size": 9702, "mtime": 1748874898320, "results": "23", "hashOfConfig": "18"}, {"size": 4087, "mtime": 1748874792974, "results": "24", "hashOfConfig": "18"}, {"size": 4603, "mtime": 1748874754597, "results": "25", "hashOfConfig": "18"}, {"size": 327, "mtime": 1748874721815, "results": "26", "hashOfConfig": "18"}, {"size": 3390, "mtime": 1748874772633, "results": "27", "hashOfConfig": "18"}, {"size": 1631, "mtime": 1748874623394, "results": "28", "hashOfConfig": "18"}, {"size": 2123, "mtime": 1748874623360, "results": "29", "hashOfConfig": "18"}, {"size": 1989, "mtime": 1748874623389, "results": "30", "hashOfConfig": "18"}, {"size": 967, "mtime": 1748874933429, "results": "31", "hashOfConfig": "18"}, {"size": 759, "mtime": 1748874933454, "results": "32", "hashOfConfig": "18"}, {"size": 166, "mtime": 1748874607622, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "mmh5b1", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\portfolio\\janvi-portfolio\\src\\app\\about\\page.tsx", ["82", "83", "84"], [], "D:\\portfolio\\janvi-portfolio\\src\\app\\contact\\page.tsx", ["85", "86", "87", "88", "89", "90"], [], "D:\\portfolio\\janvi-portfolio\\src\\app\\layout.tsx", [], [], "D:\\portfolio\\janvi-portfolio\\src\\app\\page.tsx", [], [], "D:\\portfolio\\janvi-portfolio\\src\\app\\projects\\page.tsx", [], [], "D:\\portfolio\\janvi-portfolio\\src\\app\\resume\\page.tsx", [], [], "D:\\portfolio\\janvi-portfolio\\src\\components\\hero-section.tsx", ["91"], [], "D:\\portfolio\\janvi-portfolio\\src\\components\\navigation.tsx", [], [], "D:\\portfolio\\janvi-portfolio\\src\\components\\theme-provider.tsx", [], [], "D:\\portfolio\\janvi-portfolio\\src\\components\\three-background.tsx", ["92"], [], "D:\\portfolio\\janvi-portfolio\\src\\components\\ui\\badge.tsx", [], [], "D:\\portfolio\\janvi-portfolio\\src\\components\\ui\\button.tsx", [], [], "D:\\portfolio\\janvi-portfolio\\src\\components\\ui\\card.tsx", [], [], "D:\\portfolio\\janvi-portfolio\\src\\components\\ui\\input.tsx", [], [], "D:\\portfolio\\janvi-portfolio\\src\\components\\ui\\textarea.tsx", [], [], "D:\\portfolio\\janvi-portfolio\\src\\lib\\utils.ts", [], [], {"ruleId": "93", "severity": 2, "message": "94", "line": 82, "column": 20, "nodeType": "95", "messageId": "96", "suggestions": "97"}, {"ruleId": "93", "severity": 2, "message": "94", "line": 82, "column": 67, "nodeType": "95", "messageId": "96", "suggestions": "98"}, {"ruleId": "93", "severity": 2, "message": "94", "line": 88, "column": 60, "nodeType": "95", "messageId": "96", "suggestions": "99"}, {"ruleId": "93", "severity": 2, "message": "94", "line": 98, "column": 16, "nodeType": "95", "messageId": "96", "suggestions": "100"}, {"ruleId": "93", "severity": 2, "message": "94", "line": 231, "column": 33, "nodeType": "95", "messageId": "96", "suggestions": "101"}, {"ruleId": "93", "severity": 2, "message": "94", "line": 235, "column": 22, "nodeType": "95", "messageId": "96", "suggestions": "102"}, {"ruleId": "93", "severity": 2, "message": "94", "line": 236, "column": 32, "nodeType": "95", "messageId": "96", "suggestions": "103"}, {"ruleId": "93", "severity": 2, "message": "94", "line": 237, "column": 67, "nodeType": "95", "messageId": "96", "suggestions": "104"}, {"ruleId": "93", "severity": 2, "message": "94", "line": 240, "column": 84, "nodeType": "95", "messageId": "96", "suggestions": "105"}, {"ruleId": "93", "severity": 2, "message": "94", "line": 52, "column": 42, "nodeType": "95", "messageId": "96", "suggestions": "106"}, {"ruleId": "107", "severity": 2, "message": "108", "line": 12, "column": 21, "nodeType": null, "messageId": "109", "endLine": 12, "endColumn": 27}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["110", "111", "112", "113"], ["114", "115", "116", "117"], ["118", "119", "120", "121"], ["122", "123", "124", "125"], ["126", "127", "128", "129"], ["130", "131", "132", "133"], ["134", "135", "136", "137"], ["138", "139", "140", "141"], ["142", "143", "144", "145"], ["146", "147", "148", "149"], "@typescript-eslint/no-unused-vars", "'colors' is assigned a value but never used.", "unusedVar", {"messageId": "150", "data": "151", "fix": "152", "desc": "153"}, {"messageId": "150", "data": "154", "fix": "155", "desc": "156"}, {"messageId": "150", "data": "157", "fix": "158", "desc": "159"}, {"messageId": "150", "data": "160", "fix": "161", "desc": "162"}, {"messageId": "150", "data": "163", "fix": "164", "desc": "153"}, {"messageId": "150", "data": "165", "fix": "166", "desc": "156"}, {"messageId": "150", "data": "167", "fix": "168", "desc": "159"}, {"messageId": "150", "data": "169", "fix": "170", "desc": "162"}, {"messageId": "150", "data": "171", "fix": "172", "desc": "153"}, {"messageId": "150", "data": "173", "fix": "174", "desc": "156"}, {"messageId": "150", "data": "175", "fix": "176", "desc": "159"}, {"messageId": "150", "data": "177", "fix": "178", "desc": "162"}, {"messageId": "150", "data": "179", "fix": "180", "desc": "153"}, {"messageId": "150", "data": "181", "fix": "182", "desc": "156"}, {"messageId": "150", "data": "183", "fix": "184", "desc": "159"}, {"messageId": "150", "data": "185", "fix": "186", "desc": "162"}, {"messageId": "150", "data": "187", "fix": "188", "desc": "153"}, {"messageId": "150", "data": "189", "fix": "190", "desc": "156"}, {"messageId": "150", "data": "191", "fix": "192", "desc": "159"}, {"messageId": "150", "data": "193", "fix": "194", "desc": "162"}, {"messageId": "150", "data": "195", "fix": "196", "desc": "153"}, {"messageId": "150", "data": "197", "fix": "198", "desc": "156"}, {"messageId": "150", "data": "199", "fix": "200", "desc": "159"}, {"messageId": "150", "data": "201", "fix": "202", "desc": "162"}, {"messageId": "150", "data": "203", "fix": "204", "desc": "153"}, {"messageId": "150", "data": "205", "fix": "206", "desc": "156"}, {"messageId": "150", "data": "207", "fix": "208", "desc": "159"}, {"messageId": "150", "data": "209", "fix": "210", "desc": "162"}, {"messageId": "150", "data": "211", "fix": "212", "desc": "153"}, {"messageId": "150", "data": "213", "fix": "214", "desc": "156"}, {"messageId": "150", "data": "215", "fix": "216", "desc": "159"}, {"messageId": "150", "data": "217", "fix": "218", "desc": "162"}, {"messageId": "150", "data": "219", "fix": "220", "desc": "153"}, {"messageId": "150", "data": "221", "fix": "222", "desc": "156"}, {"messageId": "150", "data": "223", "fix": "224", "desc": "159"}, {"messageId": "150", "data": "225", "fix": "226", "desc": "162"}, {"messageId": "150", "data": "227", "fix": "228", "desc": "153"}, {"messageId": "150", "data": "229", "fix": "230", "desc": "156"}, {"messageId": "150", "data": "231", "fix": "232", "desc": "159"}, {"messageId": "150", "data": "233", "fix": "234", "desc": "162"}, "replaceWithAlt", {"alt": "235"}, {"range": "236", "text": "237"}, "Replace with `&apos;`.", {"alt": "238"}, {"range": "239", "text": "240"}, "Replace with `&lsquo;`.", {"alt": "241"}, {"range": "242", "text": "243"}, "Replace with `&#39;`.", {"alt": "244"}, {"range": "245", "text": "246"}, "Replace with `&rsquo;`.", {"alt": "235"}, {"range": "247", "text": "248"}, {"alt": "238"}, {"range": "249", "text": "250"}, {"alt": "241"}, {"range": "251", "text": "252"}, {"alt": "244"}, {"range": "253", "text": "254"}, {"alt": "235"}, {"range": "255", "text": "256"}, {"alt": "238"}, {"range": "257", "text": "258"}, {"alt": "241"}, {"range": "259", "text": "260"}, {"alt": "244"}, {"range": "261", "text": "262"}, {"alt": "235"}, {"range": "263", "text": "264"}, {"alt": "238"}, {"range": "265", "text": "266"}, {"alt": "241"}, {"range": "267", "text": "268"}, {"alt": "244"}, {"range": "269", "text": "270"}, {"alt": "235"}, {"range": "271", "text": "272"}, {"alt": "238"}, {"range": "273", "text": "274"}, {"alt": "241"}, {"range": "275", "text": "276"}, {"alt": "244"}, {"range": "277", "text": "278"}, {"alt": "235"}, {"range": "279", "text": "280"}, {"alt": "238"}, {"range": "281", "text": "282"}, {"alt": "241"}, {"range": "283", "text": "284"}, {"alt": "244"}, {"range": "285", "text": "286"}, {"alt": "235"}, {"range": "287", "text": "288"}, {"alt": "238"}, {"range": "289", "text": "290"}, {"alt": "241"}, {"range": "291", "text": "292"}, {"alt": "244"}, {"range": "293", "text": "294"}, {"alt": "235"}, {"range": "295", "text": "296"}, {"alt": "238"}, {"range": "297", "text": "298"}, {"alt": "241"}, {"range": "299", "text": "300"}, {"alt": "244"}, {"range": "301", "text": "302"}, {"alt": "235"}, {"range": "303", "text": "304"}, {"alt": "238"}, {"range": "305", "text": "306"}, {"alt": "241"}, {"range": "307", "text": "308"}, {"alt": "244"}, {"range": "309", "text": "310"}, {"alt": "235"}, {"range": "311", "text": "312"}, {"alt": "238"}, {"range": "313", "text": "314"}, {"alt": "241"}, {"range": "315", "text": "316"}, {"alt": "244"}, {"range": "317", "text": "318"}, "&apos;", [2521, 2851], "\n                  <PERSON>&<PERSON><PERSON>s;m an aspiring software developer with a Master's degree in Computer Applications \n                  from Amity University Raipur. My journey in technology began with a curiosity about \n                  how things work and evolved into a passion for creating solutions that make a difference.\n                ", "&lsquo;", [2521, 2851], "\n                  <PERSON>&l<PERSON>q<PERSON>;m an aspiring software developer with a Master's degree in Computer Applications \n                  from Amity University Raipur. My journey in technology began with a curiosity about \n                  how things work and evolved into a passion for creating solutions that make a difference.\n                ", "&#39;", [2521, 2851], "\n                  I&#39;m an aspiring software developer with a Master's degree in Computer Applications \n                  from Amity University Raipur. My journey in technology began with a curiosity about \n                  how things work and evolved into a passion for creating solutions that make a difference.\n                ", "&rsquo;", [2521, 2851], "\n                  I&rsquo;m an aspiring software developer with a Master's degree in Computer Applications \n                  from Amity University Raipur. My journey in technology began with a curiosity about \n                  how things work and evolved into a passion for creating solutions that make a difference.\n                ", [2521, 2851], "\n                  I'm an aspiring software developer with a Master&apos;s degree in Computer Applications \n                  from Amity University Raipur. My journey in technology began with a curiosity about \n                  how things work and evolved into a passion for creating solutions that make a difference.\n                ", [2521, 2851], "\n                  I'm an aspiring software developer with a Master&lsquo;s degree in Computer Applications \n                  from Amity University Raipur. My journey in technology began with a curiosity about \n                  how things work and evolved into a passion for creating solutions that make a difference.\n                ", [2521, 2851], "\n                  I'm an aspiring software developer with a Master&#39;s degree in Computer Applications \n                  from Amity University Raipur. My journey in technology began with a curiosity about \n                  how things work and evolved into a passion for creating solutions that make a difference.\n                ", [2521, 2851], "\n                  I'm an aspiring software developer with a Master&rsquo;s degree in Computer Applications \n                  from Amity University Raipur. My journey in technology began with a curiosity about \n                  how things work and evolved into a passion for creating solutions that make a difference.\n                ", [2911, 3317], "\n                  With expertise in modern web technologies like React, Node.js, and Python, I focus on \n                  building secure, scalable applications. I&apos;m particularly interested in the intersection \n                  of web development and artificial intelligence, always looking for ways to leverage \n                  cutting-edge technologies to solve real-world problems.\n                ", [2911, 3317], "\n                  With expertise in modern web technologies like React, Node.js, and Python, I focus on \n                  building secure, scalable applications. I&lsquo;m particularly interested in the intersection \n                  of web development and artificial intelligence, always looking for ways to leverage \n                  cutting-edge technologies to solve real-world problems.\n                ", [2911, 3317], "\n                  With expertise in modern web technologies like React, Node.js, and Python, I focus on \n                  building secure, scalable applications. I&#39;m particularly interested in the intersection \n                  of web development and artificial intelligence, always looking for ways to leverage \n                  cutting-edge technologies to solve real-world problems.\n                ", [2911, 3317], "\n                  With expertise in modern web technologies like React, Node.js, and Python, I focus on \n                  building secure, scalable applications. I&rsquo;m particularly interested in the intersection \n                  of web development and artificial intelligence, always looking for ways to leverage \n                  cutting-edge technologies to solve real-world problems.\n                ", [2594, 2732], "\n              I&apos;m always open to discussing new opportunities, interesting projects, or just having a chat about technology.\n            ", [2594, 2732], "\n              I&lsquo;m always open to discussing new opportunities, interesting projects, or just having a chat about technology.\n            ", [2594, 2732], "\n              I&#39;m always open to discussing new opportunities, interesting projects, or just having a chat about technology.\n            ", [2594, 2732], "\n              I&rsquo;m always open to discussing new opportunities, interesting projects, or just having a chat about technology.\n            ", [8002, 8015], "Let&apos;s Connect", [8002, 8015], "Let&lsquo;s Connect", [8002, 8015], "Let&#39;s Connect", [8002, 8015], "Let&rsquo;s Connect", [8148, 8466], "\n                    I&apos;m always interested in hearing about new opportunities and exciting projects. \n                    Whether you're a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I'd love to hear from you.\n                  ", [8148, 8466], "\n                    I&lsquo;m always interested in hearing about new opportunities and exciting projects. \n                    Whether you're a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I'd love to hear from you.\n                  ", [8148, 8466], "\n                    I&#39;m always interested in hearing about new opportunities and exciting projects. \n                    Whether you're a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I'd love to hear from you.\n                  ", [8148, 8466], "\n                    I&rsquo;m always interested in hearing about new opportunities and exciting projects. \n                    Whether you're a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I'd love to hear from you.\n                  ", [8148, 8466], "\n                    I'm always interested in hearing about new opportunities and exciting projects. \n                    Whether you&apos;re a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I'd love to hear from you.\n                  ", [8148, 8466], "\n                    I'm always interested in hearing about new opportunities and exciting projects. \n                    Whether you&lsquo;re a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I'd love to hear from you.\n                  ", [8148, 8466], "\n                    I'm always interested in hearing about new opportunities and exciting projects. \n                    Whether you&#39;re a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I'd love to hear from you.\n                  ", [8148, 8466], "\n                    I'm always interested in hearing about new opportunities and exciting projects. \n                    Whether you&rsquo;re a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I'd love to hear from you.\n                  ", [8148, 8466], "\n                    I'm always interested in hearing about new opportunities and exciting projects. \n                    Whether you're a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I&apos;d love to hear from you.\n                  ", [8148, 8466], "\n                    I'm always interested in hearing about new opportunities and exciting projects. \n                    Whether you're a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I&lsquo;d love to hear from you.\n                  ", [8148, 8466], "\n                    I'm always interested in hearing about new opportunities and exciting projects. \n                    Whether you're a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I&#39;d love to hear from you.\n                  ", [8148, 8466], "\n                    I'm always interested in hearing about new opportunities and exciting projects. \n                    Whether you're a company looking to hire, a fellow developer wanting to collaborate, \n                    or someone with an interesting project idea, I&rsquo;d love to hear from you.\n                  ", [8526, 8669], "\n                    Feel free to reach out through any of the channels above, and I&apos;ll get back to you as soon as possible!\n                  ", [8526, 8669], "\n                    Feel free to reach out through any of the channels above, and I&lsquo;ll get back to you as soon as possible!\n                  ", [8526, 8669], "\n                    Feel free to reach out through any of the channels above, and I&#39;ll get back to you as soon as possible!\n                  ", [8526, 8669], "\n                    Feel free to reach out through any of the channels above, and I&rsquo;ll get back to you as soon as possible!\n                  ", [1446, 1453], "Hi, I&apos;m", [1446, 1453], "Hi, I&lsquo;m", [1446, 1453], "Hi, I&#39;m", [1446, 1453], "Hi, I&rsquo;m"]