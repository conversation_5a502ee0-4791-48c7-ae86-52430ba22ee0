"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { GraduationCap, Code, Brain, Zap } from "lucide-react";

const skills = [
  "C++", "Java", "Python", "JavaScript", "TypeScript",
  "React", "Node.js", "Next.js", "Express.js",
  "MongoDB", "SQL", "PostgreSQL",
  "HTML", "CSS", "Tailwind CSS",
  "Git", "GitHub", "Selenium", "REST APIs"
];

const education = [
  {
    degree: "Master of Computer Applications (MCA)",
    institution: "Amity University Raipur",
    year: "2022-2024",
    icon: GraduationCap,
  },
  {
    degree: "Bachelor of Computer Applications (BCA)",
    institution: "ABVV Bilaspur",
    year: "2019-2022",
    icon: GraduationCap,
  },
];

const interests = [
  {
    title: "Web Development",
    description: "Building responsive and interactive web applications",
    icon: Code,
  },
  {
    title: "AI & Machine Learning",
    description: "Exploring artificial intelligence and machine learning applications",
    icon: Brain,
  },
  {
    title: "Performance Optimization",
    description: "Creating fast, efficient, and scalable applications",
    icon: Zap,
  },
];

export default function About() {
  return (
    <div className="pt-16 min-h-screen">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto"
        >
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl font-bold mb-4">
              About <span className="gradient-text">Me</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Passionate about creating innovative solutions and building the future of technology
            </p>
          </div>

          {/* Bio Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-12"
          >
            <Card>
              <CardHeader>
                <CardTitle>My Journey</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-neutral dark:prose-invert max-w-none">
                <p className="text-lg leading-relaxed">
                  I'm an aspiring software developer with a Master's degree in Computer Applications 
                  from Amity University Raipur. My journey in technology began with a curiosity about 
                  how things work and evolved into a passion for creating solutions that make a difference.
                </p>
                <p className="text-lg leading-relaxed">
                  With expertise in modern web technologies like React, Node.js, and Python, I focus on 
                  building secure, scalable applications. I'm particularly interested in the intersection 
                  of web development and artificial intelligence, always looking for ways to leverage 
                  cutting-edge technologies to solve real-world problems.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Education Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-12"
          >
            <h2 className="text-3xl font-bold mb-6">Education</h2>
            <div className="grid gap-6 md:grid-cols-2">
              {education.map((edu, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <edu.icon className="h-6 w-6 text-primary" />
                      <CardTitle className="text-lg">{edu.degree}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{edu.institution}</p>
                    <p className="text-sm text-muted-foreground">{edu.year}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>

          {/* Skills Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mb-12"
          >
            <h2 className="text-3xl font-bold mb-6">Skills & Technologies</h2>
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-wrap gap-2">
                  {skills.map((skill, index) => (
                    <motion.div
                      key={skill}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <Badge variant="secondary" className="text-sm">
                        {skill}
                      </Badge>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Interests Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <h2 className="text-3xl font-bold mb-6">Areas of Interest</h2>
            <div className="grid gap-6 md:grid-cols-3">
              {interests.map((interest, index) => (
                <Card key={index} className="text-center">
                  <CardHeader>
                    <div className="flex justify-center mb-2">
                      <interest.icon className="h-8 w-8 text-primary" />
                    </div>
                    <CardTitle className="text-lg">{interest.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{interest.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
