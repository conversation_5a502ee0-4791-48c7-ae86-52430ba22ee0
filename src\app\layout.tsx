import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/navigation";
import { ThemeProvider } from "@/components/theme-provider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Jan<PERSON>wani - Software Developer Portfolio",
  description: "Aspiring Software Developer building secure, scalable web and AI applications. MCA graduate with expertise in C++, Java, Python, React, Node.js, and more.",
  keywords: ["Janvi Kalwani", "Software Developer", "Portfolio", "React", "Node.js", "Python", "Web Development", "AI Applications"],
  authors: [{ name: "<PERSON><PERSON>" }],
  creator: "<PERSON><PERSON>",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://janvi-kalwani.vercel.app",
    title: "Janvi Kalwani - Software Developer Portfolio",
    description: "Aspiring Software Developer building secure, scalable web and AI applications.",
    siteName: "Janvi Kalwani Portfolio",
  },
  twitter: {
    card: "summary_large_image",
    title: "Janvi Kalwani - Software Developer Portfolio",
    description: "Aspiring Software Developer building secure, scalable web and AI applications.",
  },
  formatDetection: {
    telephone: false,
    date: false,
    address: false,
    email: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning={true} className="dark">
      <head>
        <meta name="format-detection" content="telephone=no, date=no, email=no, address=no" />
      </head>
      <body
        suppressHydrationWarning={true}
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-[#030014] text-white overflow-x-hidden`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          forcedTheme="dark"
          disableTransitionOnChange
        >
          <div className="relative w-full">
            <div className="absolute top-0 z-[-2] h-screen w-screen bg-[#030014] bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(120,119,198,0.3),rgba(255,255,255,0))]" />
            <Navigation />
            <main className="min-h-screen">{children}</main>
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
