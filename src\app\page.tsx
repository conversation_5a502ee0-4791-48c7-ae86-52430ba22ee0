"use client";

import { HeroS<PERSON><PERSON> } from "@/components/hero-section";
import { ScrollReveal } from "@/components/scroll-reveal";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { GraduationCap, Code, Brain, Zap, Sparkles, Award, Target, Mail, Github, Linkedin } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

const skills = [
  { icon: Code, title: "Programming Languages", items: ["C++", "Java", "Python", "JavaScript", "TypeScript"] },
  { icon: Brain, title: "AI & ML", items: ["TensorFlow", "PyTorch", "Scikit-learn", "NLP", "Computer Vision"] },
  { icon: Zap, title: "Web Development", items: ["React", "Next.js", "Node.js", "Express", "MongoDB"] },
  { icon: <PERSON>rk<PERSON>, title: "UI/UX", items: ["Tailwind CSS", "Material UI", "Framer Motion", "Three.js", "Responsive Design"] },
];

const projects = [
  {
    title: "Portfolio Website",
    description: "Modern portfolio website built with Next.js, Three.js, and Framer Motion featuring smooth animations and 3D elements.",
    tags: ["Next.js", "Three.js", "Framer Motion", "TypeScript"],
    links: { github: "#", demo: "#" }
  },
  // Add more projects here
];

export default function Home() {
  return (
    <div className="relative">
      <HeroSection />
      
      {/* About Section */}
      <section id="about" className="min-h-screen py-20 bg-background">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="text-4xl font-bold text-center mb-12 gradient-text">About Me</h2>
          </ScrollReveal>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            <ScrollReveal direction="left">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl mb-2">My Journey</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Passionate software developer with expertise in full-stack development and AI applications.
                    Currently pursuing MCA with focus on modern web technologies and artificial intelligence.
                  </p>
                </CardContent>
              </Card>
            </ScrollReveal>
            
            <ScrollReveal direction="right">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl mb-2">Education</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-4">
                    <GraduationCap className="h-6 w-6 text-primary" />
                    <div>
                      <h3 className="font-semibold">Master of Computer Applications</h3>
                      <p className="text-sm text-muted-foreground">2022 - Present</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </ScrollReveal>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {skills.map((skill, index) => (
              <ScrollReveal key={skill.title} delay={index * 0.1}>
                <Card>
                  <CardHeader className="space-y-1">
                    <div className="flex items-center gap-2">
                      <skill.icon className="h-5 w-5 text-primary" />
                      <CardTitle className="text-xl">{skill.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {skill.items.map((item) => (
                        <Badge key={item} variant="secondary">{item}</Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>
      
      {/* Projects Section */}
      <section id="projects" className="min-h-screen py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="text-4xl font-bold text-center mb-12 gradient-text">Projects</h2>
          </ScrollReveal>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {projects.map((project, index) => (
              <ScrollReveal key={project.title} delay={index * 0.1}>
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle className="text-xl">{project.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-muted-foreground">{project.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {project.tags.map((tag) => (
                        <Badge key={tag} variant="secondary">{tag}</Badge>
                      ))}
                    </div>
                    <div className="flex gap-4">
                      <Link href={project.links.github} target="_blank">
                        <Button variant="ghost" size="sm">
                          <Github className="h-4 w-4 mr-2" />
                          GitHub
                        </Button>
                      </Link>
                      <Link href={project.links.demo} target="_blank">
                        <Button variant="ghost" size="sm">
                          <Target className="h-4 w-4 mr-2" />
                          Demo
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>
      
      {/* Contact Section */}
      <section id="contact" className="min-h-screen py-20 bg-background">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="text-4xl font-bold text-center mb-12 gradient-text">Get in Touch</h2>
          </ScrollReveal>
          
          <div className="max-w-2xl mx-auto text-center">
            <ScrollReveal>
              <p className="text-lg text-muted-foreground mb-8">
                I'm always open to new opportunities and collaborations.
                Feel free to reach out if you'd like to work together!
              </p>
              
              <div className="flex justify-center gap-4">
                <Link href="mailto:<EMAIL>">
                  <Button className="gap-2">
                    <Mail className="h-4 w-4" />
                    Email Me
                  </Button>
                </Link>
                <Link href="https://linkedin.com/in/your-profile" target="_blank">
                  <Button variant="outline" className="gap-2">
                    <Linkedin className="h-4 w-4" />
                    LinkedIn
                  </Button>
                </Link>
                <Link href="https://github.com/your-username" target="_blank">
                  <Button variant="outline" className="gap-2">
                    <Github className="h-4 w-4" />
                    GitHub
                  </Button>
                </Link>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>
    </div>
  );
}
