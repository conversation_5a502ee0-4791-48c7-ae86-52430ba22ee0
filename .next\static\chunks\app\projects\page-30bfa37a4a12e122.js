(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[893],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var r=a(5155);a(2115);var i=a(4624),s=a(2085),n=a(9434);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:s,asChild:l=!1,...c}=e,d=l?i.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:a,size:s,className:t})),...c})}},6126:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var r=a(5155);a(2115);var i=a(4624),s=a(2085),n=a(9434);let o=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:s=!1,...l}=e,c=s?i.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(o({variant:a}),t),...l})}},6695:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>l,ZB:()=>o,Zp:()=>s,aR:()=>n});var r=a(5155);a(2115);var i=a(9434);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...a})}},7488:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(5155),i=a(6408),s=a(6695),n=a(6126),o=a(285),l=a(9099);let c=(0,a(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var d=a(6874),p=a.n(d);let h=[{title:"E-Commerce Platform",description:"A full-stack e-commerce application built with React, Node.js, and MongoDB. Features include user authentication, product catalog, shopping cart, and payment integration.",technologies:["React","Node.js","MongoDB","Express.js","Stripe API"],githubUrl:"https://github.com/janvi-kalwani/ecommerce-platform",liveUrl:"https://ecommerce-demo.vercel.app",image:"/projects/ecommerce.jpg"},{title:"Task Management App",description:"A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",technologies:["Next.js","TypeScript","Prisma","PostgreSQL","Socket.io"],githubUrl:"https://github.com/janvi-kalwani/task-manager",liveUrl:"https://task-manager-demo.vercel.app",image:"/projects/task-manager.jpg"},{title:"Weather Dashboard",description:"A responsive weather dashboard that displays current weather conditions, forecasts, and interactive maps using external APIs.",technologies:["React","JavaScript","OpenWeather API","Chart.js","CSS3"],githubUrl:"https://github.com/janvi-kalwani/weather-dashboard",liveUrl:"https://weather-dashboard-demo.vercel.app",image:"/projects/weather.jpg"},{title:"AI Chat Assistant",description:"An intelligent chat assistant powered by machine learning, capable of natural language processing and contextual responses.",technologies:["Python","TensorFlow","Flask","React","Natural Language Processing"],githubUrl:"https://github.com/janvi-kalwani/ai-chat-assistant",liveUrl:"https://ai-chat-demo.vercel.app",image:"/projects/ai-chat.jpg"},{title:"Portfolio Website",description:"A modern, responsive portfolio website built with Next.js, featuring 3D animations, dark mode, and smooth scroll effects.",technologies:["Next.js","TypeScript","Tailwind CSS","Framer Motion","Three.js"],githubUrl:"https://github.com/janvi-kalwani/portfolio",liveUrl:"https://janvi-kalwani.vercel.app",image:"/projects/portfolio.jpg"}],u={hidden:{opacity:0},visible:{opacity:1,transition:{delayChildren:.3,staggerChildren:.1}}},g={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.5}}};function m(){return(0,r.jsx)("div",{className:"pt-16 min-h-screen",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-center mb-12",children:[(0,r.jsxs)("h1",{className:"text-4xl sm:text-5xl font-bold mb-4",children:["My ",(0,r.jsx)("span",{className:"gradient-text",children:"Projects"})]}),(0,r.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"A showcase of my work in web development, AI applications, and innovative solutions"})]}),(0,r.jsx)(i.P.div,{variants:u,initial:"hidden",animate:"visible",className:"grid gap-8 md:grid-cols-2 lg:grid-cols-3",children:h.map((e,t)=>(0,r.jsx)(i.P.div,{variants:g,children:(0,r.jsxs)(s.Zp,{className:"h-full flex flex-col group hover:shadow-lg transition-shadow duration-300",children:[(0,r.jsxs)(s.aR,{children:[(0,r.jsx)("div",{className:"aspect-video bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg mb-4 flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-4xl font-bold text-primary/50",children:e.title.charAt(0)})}),(0,r.jsx)(s.ZB,{className:"text-xl group-hover:text-primary transition-colors",children:e.title})]}),(0,r.jsxs)(s.Wu,{className:"flex-1 flex flex-col",children:[(0,r.jsx)("p",{className:"text-muted-foreground mb-4 flex-1",children:e.description}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.technologies.map(e=>(0,r.jsx)(n.E,{variant:"outline",className:"text-xs",children:e},e))})}),(0,r.jsxs)("div",{className:"flex gap-2 mt-auto",children:[(0,r.jsx)(p(),{href:e.githubUrl,target:"_blank",rel:"noopener noreferrer",children:(0,r.jsxs)(o.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Code"]})}),(0,r.jsx)(p(),{href:e.liveUrl,target:"_blank",rel:"noopener noreferrer",children:(0,r.jsxs)(o.$,{size:"sm",className:"flex-1",children:[(0,r.jsx)(c,{className:"mr-2 h-4 w-4"}),"Live Demo"]})})]})]})]})},t))}),(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.5},className:"text-center mt-12",children:[(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Want to see more of my work?"}),(0,r.jsx)(p(),{href:"https://github.com/janvi-kalwani",target:"_blank",rel:"noopener noreferrer",children:(0,r.jsxs)(o.$,{variant:"outline",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"View All Projects on GitHub"]})})]})]})})}},9099:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var r=a(2596),i=a(9688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,i.QP)((0,r.$)(t))}},9733:(e,t,a)=>{Promise.resolve().then(a.bind(a,7488))}},e=>{var t=t=>e(e.s=t);e.O(0,[154,874,441,684,358],()=>t(9733)),_N_E=e.O()}]);