"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ExternalLink, Github } from "lucide-react";
import Link from "next/link";

const projects = [
  {
    title: "E-Commerce Platform",
    description: "A full-stack e-commerce application built with React, Node.js, and MongoDB. Features include user authentication, product catalog, shopping cart, and payment integration.",
    technologies: ["React", "Node.js", "MongoDB", "Express.js", "Stripe API"],
    githubUrl: "https://github.com/janvi-kalwani/ecommerce-platform",
    liveUrl: "https://ecommerce-demo.vercel.app",
    image: "/projects/ecommerce.jpg",
  },
  {
    title: "Task Management App",
    description: "A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",
    technologies: ["Next.js", "TypeScript", "Prisma", "PostgreSQL", "Socket.io"],
    githubUrl: "https://github.com/janvi-kalwani/task-manager",
    liveUrl: "https://task-manager-demo.vercel.app",
    image: "/projects/task-manager.jpg",
  },
  {
    title: "Weather Dashboard",
    description: "A responsive weather dashboard that displays current weather conditions, forecasts, and interactive maps using external APIs.",
    technologies: ["React", "JavaScript", "OpenWeather API", "Chart.js", "CSS3"],
    githubUrl: "https://github.com/janvi-kalwani/weather-dashboard",
    liveUrl: "https://weather-dashboard-demo.vercel.app",
    image: "/projects/weather.jpg",
  },
  {
    title: "AI Chat Assistant",
    description: "An intelligent chat assistant powered by machine learning, capable of natural language processing and contextual responses.",
    technologies: ["Python", "TensorFlow", "Flask", "React", "Natural Language Processing"],
    githubUrl: "https://github.com/janvi-kalwani/ai-chat-assistant",
    liveUrl: "https://ai-chat-demo.vercel.app",
    image: "/projects/ai-chat.jpg",
  },
  {
    title: "Portfolio Website",
    description: "A modern, responsive portfolio website built with Next.js, featuring 3D animations, dark mode, and smooth scroll effects.",
    technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Framer Motion", "Three.js"],
    githubUrl: "https://github.com/janvi-kalwani/portfolio",
    liveUrl: "https://janvi-kalwani.vercel.app",
    image: "/projects/portfolio.jpg",
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      delayChildren: 0.3,
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
    },
  },
};

export default function Projects() {
  return (
    <div className="pt-16 min-h-screen">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl sm:text-5xl font-bold mb-4">
            My <span className="gradient-text">Projects</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            A showcase of my work in web development, AI applications, and innovative solutions
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid gap-8 md:grid-cols-2 lg:grid-cols-3"
        >
          {projects.map((project, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card className="h-full flex flex-col group hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <div className="aspect-video bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg mb-4 flex items-center justify-center">
                    <div className="text-4xl font-bold text-primary/50">
                      {project.title.charAt(0)}
                    </div>
                  </div>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors">
                    {project.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col">
                  <p className="text-muted-foreground mb-4 flex-1">
                    {project.description}
                  </p>
                  
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech) => (
                        <Badge key={tech} variant="outline" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2 mt-auto">
                    <Link href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Github className="mr-2 h-4 w-4" />
                        Code
                      </Button>
                    </Link>
                    <Link href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                      <Button size="sm" className="flex-1">
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Live Demo
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="text-center mt-12"
        >
          <p className="text-muted-foreground mb-4">
            Want to see more of my work?
          </p>
          <Link href="https://github.com/janvi-kalwani" target="_blank" rel="noopener noreferrer">
            <Button variant="outline">
              <Github className="mr-2 h-4 w-4" />
              View All Projects on GitHub
            </Button>
          </Link>
        </motion.div>
      </div>
    </div>
  );
}
