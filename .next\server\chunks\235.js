exports.id=235,exports.ids=[235],exports.modules={139:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var i=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},3187:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},3701:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>i});let i=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\portfolio\\janvi-portfolio\\src\\components\\theme-provider.tsx","ThemeProvider")},4246:(e,r,t)=>{"use strict";t.d(r,{Navigation:()=>g});var i=t(687),a=t(3210),n=t(5814),s=t.n(n),o=t(6189),d=t(6001),l=t(8920),c=t(218),v=t(9523),u=t(1134),h=t(363),m=t(1860),p=t(2941),b=t(4780);let f=[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/projects",label:"Projects"},{href:"/resume",label:"Resume"},{href:"/contact",label:"Contact"}];function g(){let[e,r]=(0,a.useState)(!1),[t,n]=(0,a.useState)(!1),g=(0,o.usePathname)(),{theme:x,setTheme:y}=(0,c.D)();return t?(0,i.jsx)(d.P.nav,{initial:{y:-100},animate:{y:0},transition:{duration:.5},className:"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,i.jsx)(s(),{href:"/",className:"flex items-center space-x-2",children:(0,i.jsx)(d.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"text-xl font-bold gradient-text",children:"JK"})}),(0,i.jsx)("div",{className:"hidden md:flex items-center space-x-8",children:f.map(e=>(0,i.jsxs)(s(),{href:e.href,className:(0,b.cn)("relative px-3 py-2 text-sm font-medium transition-colors hover:text-primary",g===e.href?"text-primary":"text-muted-foreground"),children:[e.label,g===e.href&&(0,i.jsx)(d.P.div,{layoutId:"activeTab",className:"absolute inset-x-0 -bottom-px h-px bg-primary",initial:!1,transition:{type:"spring",stiffness:500,damping:30}})]},e.href))}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)(v.$,{variant:"ghost",size:"icon",onClick:()=>{y("dark"===x?"light":"dark")},className:"w-9 h-9",children:"dark"===x?(0,i.jsx)(u.A,{className:"h-4 w-4"}):(0,i.jsx)(h.A,{className:"h-4 w-4"})}),(0,i.jsx)(v.$,{variant:"ghost",size:"icon",className:"md:hidden w-9 h-9",onClick:()=>r(!e),children:e?(0,i.jsx)(m.A,{className:"h-4 w-4"}):(0,i.jsx)(p.A,{className:"h-4 w-4"})})]})]}),(0,i.jsx)(l.N,{children:e&&(0,i.jsx)(d.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.2},className:"md:hidden border-t border-border",children:(0,i.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:f.map(e=>(0,i.jsx)(s(),{href:e.href,onClick:()=>r(!1),className:(0,b.cn)("block px-3 py-2 text-base font-medium rounded-md transition-colors",g===e.href?"text-primary bg-primary/10":"text-muted-foreground hover:text-primary hover:bg-primary/5"),children:e.label},e.href))})})})]})}):null}},4377:(e,r,t)=>{Promise.resolve().then(t.bind(t,4544)),Promise.resolve().then(t.bind(t,3701))},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v,metadata:()=>c});var i=t(7413),a=t(2376),n=t.n(a),s=t(8726),o=t.n(s);t(1135);var d=t(4544),l=t(3701);let c={title:"Janvi Kalwani - Software Developer Portfolio",description:"Aspiring Software Developer building secure, scalable web and AI applications. MCA graduate with expertise in C++, Java, Python, React, Node.js, and more.",keywords:["Janvi Kalwani","Software Developer","Portfolio","React","Node.js","Python","Web Development","AI Applications"],authors:[{name:"Janvi Kalwani"}],creator:"Janvi Kalwani",openGraph:{type:"website",locale:"en_US",url:"https://janvi-kalwani.vercel.app",title:"Janvi Kalwani - Software Developer Portfolio",description:"Aspiring Software Developer building secure, scalable web and AI applications.",siteName:"Janvi Kalwani Portfolio"},twitter:{card:"summary_large_image",title:"Janvi Kalwani - Software Developer Portfolio",description:"Aspiring Software Developer building secure, scalable web and AI applications."}};function v({children:e}){return(0,i.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,i.jsx)("body",{className:`${n().variable} ${o().variable} antialiased`,children:(0,i.jsxs)(l.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[(0,i.jsx)(d.Navigation,{}),(0,i.jsx)("main",{className:"min-h-screen",children:e})]})})})}},4493:(e,r,t)=>{"use strict";t.d(r,{Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>s});var i=t(687);t(3210);var a=t(4780);function n({className:e,...r}){return(0,i.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function s({className:e,...r}){return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},4544:(e,r,t)=>{"use strict";t.d(r,{Navigation:()=>i});let i=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\portfolio\\janvi-portfolio\\src\\components\\navigation.tsx","Navigation")},4649:(e,r,t)=>{Promise.resolve().then(t.bind(t,4246)),Promise.resolve().then(t.bind(t,6871))},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var i=t(9384),a=t(2348);function n(...e){return(0,a.QP)((0,i.$)(e))}},6834:(e,r,t)=>{"use strict";t.d(r,{E:()=>d});var i=t(687);t(3210);var a=t(1391),n=t(4224),s=t(4780);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:r,asChild:t=!1,...n}){let d=t?a.DX:"span";return(0,i.jsx)(d,{"data-slot":"badge",className:(0,s.cn)(o({variant:r}),e),...n})}},6871:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>n});var i=t(687);t(3210);var a=t(218);function n({children:e,...r}){return(0,i.jsx)(a.N,{...r,children:e})}},9523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var i=t(687);t(3210);var a=t(1391),n=t(4224),s=t(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:n=!1,...d}){let l=n?a.DX:"button";return(0,i.jsx)(l,{"data-slot":"button",className:(0,s.cn)(o({variant:r,size:t,className:e})),...d})}}};