"use client";

import { useEffect, useRef, useState, useCallback } from 'react';

export function useScrollAnimation(threshold = 0.1) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const cleanup = useCallback(() => {
    if (observerRef.current && ref.current) {
      observerRef.current.unobserve(ref.current);
      observerRef.current.disconnect();
    }
  }, []);

  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          cleanup();
        }
      },
      {
        threshold,
        rootMargin: '100px 0px -100px 0px',
      }
    );

    if (ref.current) {
      observerRef.current.observe(ref.current);
    }

    return cleanup;
  }, [threshold, cleanup]);

  return { ref, isVisible };
}

export function useScrollProgress() {
  const [scrollProgress, setScrollProgress] = useState(0);
  const frameRef = useRef<number>();

  useEffect(() => {
    const updateScrollProgress = () => {
      if (frameRef.current) {
        cancelAnimationFrame(frameRef.current);
      }

      frameRef.current = requestAnimationFrame(() => {
        const scrollPx = document.documentElement.scrollTop;
        const winHeightPx = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrolled = Math.min(Math.max(scrollPx / winHeightPx, 0), 1);
        setScrollProgress(scrolled);
      });
    };

    window.addEventListener('scroll', updateScrollProgress, { passive: true });
    updateScrollProgress();

    return () => {
      window.removeEventListener('scroll', updateScrollProgress);
      if (frameRef.current) {
        cancelAnimationFrame(frameRef.current);
      }
    };
  }, []);

  return scrollProgress;
}
