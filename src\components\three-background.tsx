"use client";

import { useRef, useMemo, useEffect } from "react";
import { Can<PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber";
import { Points, PointMaterial, Sphere } from "@react-three/drei";
import * as THREE from "three";

function AnimatedParticles({ scrollY }: { scrollY: number }) {
  const ref = useRef<THREE.Points>(null);
  const { viewport } = useThree();

  const [positions, colors] = useMemo(() => {
    const positions = new Float32Array(5000 * 3);
    const colors = new Float32Array(5000 * 3);

    for (let i = 0; i < 5000; i++) {
      // Create a more spread out particle field
      positions[i * 3] = (Math.random() - 0.5) * 25;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 25;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 25;

      // Modern color palette: deep purples and blues
      const colorChoice = Math.random();
      if (colorChoice < 0.4) {
        // Deep Purple
        colors[i * 3] = 0.5;     // R
        colors[i * 3 + 1] = 0.2; // G
        colors[i * 3 + 2] = 0.9; // B
      } else if (colorChoice < 0.7) {
        // Electric Blue
        colors[i * 3] = 0.2;    // R
        colors[i * 3 + 1] = 0.4; // G
        colors[i * 3 + 2] = 1.0; // B
      } else {
        // Neon Purple
        colors[i * 3] = 0.6;    // R
        colors[i * 3 + 1] = 0.2; // G
        colors[i * 3 + 2] = 0.8; // B
      }
    }

    return [positions, colors];
  }, []);

  useFrame((state) => {
    if (ref.current) {
      // Smooth parallax effect based on scroll
      const scrollEffect = scrollY * 0.0005;
      ref.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.03) * 0.1 - scrollEffect;
      ref.current.rotation.y = state.clock.elapsedTime * 0.015 + scrollEffect * 0.5;
      ref.current.rotation.z = Math.sin(state.clock.elapsedTime * 0.02) * 0.05;

      // Pulse effect
      const scale = 1 + Math.sin(state.clock.elapsedTime) * 0.05;
      ref.current.scale.set(scale, scale, scale);
    }
  });

  return (
    <Points ref={ref} positions={positions} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        vertexColors
        size={0.025}
        sizeAttenuation={true}
        depthWrite={false}
        blending={THREE.AdditiveBlending}
        opacity={0.8}
      />
    </Points>
  );
}

function FloatingOrbs({ scrollY }: { scrollY: number }) {
  const groupRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (groupRef.current) {
      // Smooth parallax effect based on scroll
      const scrollEffect = scrollY * 0.0003;
      groupRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.08) * 0.05 - scrollEffect;
      groupRef.current.rotation.y = state.clock.elapsedTime * 0.025 + scrollEffect * 0.3;
      
      // Gentle floating motion
      groupRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.3;
    }
  });

  const orbs = useMemo(() => {
    const temp = [];
    for (let i = 0; i < 12; i++) {
      temp.push({
        position: [
          (Math.random() - 0.5) * 20,
          (Math.random() - 0.5) * 20,
          (Math.random() - 0.5) * 20,
        ] as [number, number, number],
        scale: Math.random() * 0.4 + 0.1,
        color: new THREE.Color().setHSL(
          0.7 + Math.random() * 0.2, // Hue: purple to blue range
          0.9,
          0.6
        ),
      });
    }
    return temp;
  }, []);

  return (
    <group ref={groupRef}>
      {orbs.map((orb, index) => (
        <mesh key={index} position={orb.position} scale={orb.scale}>
          <sphereGeometry args={[1, 32, 32]} />
          <meshPhongMaterial
            color={orb.color}
            transparent
            opacity={0.6}
            shininess={100}
          />
        </mesh>
      ))}
    </group>
  );
}

export function ThreeBackground() {
  const scrollRef = useRef(0);

  useEffect(() => {
    const handleScroll = () => {
      scrollRef.current = window.scrollY;
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="fixed top-0 left-0 w-full h-screen z-[-1] bg-[#030014]">
      <Canvas camera={{ position: [0, 0, 5], fov: 60 }}>
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} intensity={1} />
        <AnimatedParticles scrollY={scrollRef.current} />
        <FloatingOrbs scrollY={scrollRef.current} />
      </Canvas>
    </div>
  );
}
