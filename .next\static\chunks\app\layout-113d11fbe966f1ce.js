(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var n=r(5155);r(2115);var s=r(4624),a=r(2085),i=r(9434);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:a,asChild:l=!1,...c}=e,d=l?s.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:a,className:t})),...c})}},347:()=>{},1175:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2093,23)),Promise.resolve().then(r.t.bind(r,7735,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,5609)),Promise.resolve().then(r.bind(r,1483))},1362:(e,t,r)=>{"use strict";r.d(t,{D:()=>c,N:()=>d});var n=r(2115),s=(e,t,r,n,s,a,i,o)=>{let l=document.documentElement,c=["light","dark"];function d(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&a?s.map(e=>a[e]||e):s;r?(l.classList.remove(...n),l.classList.add(a&&a[t]?a[t]:t)):l.setAttribute(e,t)}),r=t,o&&c.includes(r)&&(l.style.colorScheme=r)}if(n)d(n);else try{let e=localStorage.getItem(t)||r,n=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(n)}catch(e){}},a=["light","dark"],i="(prefers-color-scheme: dark)",o=n.createContext(void 0),l={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=n.useContext(o))?e:l},d=e=>n.useContext(o)?n.createElement(n.Fragment,null,e.children):n.createElement(u,{...e}),h=["light","dark"],u=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:s=!0,enableColorScheme:l=!0,storageKey:c="theme",themes:d=h,defaultTheme:u=s?"system":"light",attribute:g="data-theme",value:b,children:x,nonce:y,scriptProps:k}=e,[w,j]=n.useState(()=>p(c,u)),[N,E]=n.useState(()=>"system"===w?v():w),C=b?Object.values(b):d,P=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&s&&(t=v());let n=b?b[t]:t,i=r?f(y):null,o=document.documentElement,c=e=>{"class"===e?(o.classList.remove(...C),n&&o.classList.add(n)):e.startsWith("data-")&&(n?o.setAttribute(e,n):o.removeAttribute(e))};if(Array.isArray(g)?g.forEach(c):c(g),l){let e=a.includes(u)?u:null,r=a.includes(t)?t:e;o.style.colorScheme=r}null==i||i()},[y]),S=n.useCallback(e=>{let t="function"==typeof e?e(w):e;j(t);try{localStorage.setItem(c,t)}catch(e){}},[w]),M=n.useCallback(e=>{E(v(e)),"system"===w&&s&&!t&&P("system")},[w,t]);n.useEffect(()=>{let e=window.matchMedia(i);return e.addListener(M),M(e),()=>e.removeListener(M)},[M]),n.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?j(e.newValue):S(u))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[S]),n.useEffect(()=>{P(null!=t?t:w)},[t,w]);let _=n.useMemo(()=>({theme:w,setTheme:S,forcedTheme:t,resolvedTheme:"system"===w?N:w,themes:s?[...d,"system"]:d,systemTheme:s?N:void 0}),[w,S,t,N,s,d]);return n.createElement(o.Provider,{value:_},n.createElement(m,{forcedTheme:t,storageKey:c,attribute:g,enableSystem:s,enableColorScheme:l,defaultTheme:u,value:b,themes:d,nonce:y,scriptProps:k}),x)},m=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:i,enableColorScheme:o,defaultTheme:l,value:c,themes:d,nonce:h,scriptProps:u}=e,m=JSON.stringify([a,r,l,t,d,c,i,o]).slice(1,-1);return n.createElement("script",{...u,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(s.toString(),")(").concat(m,")")}})}),p=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},f=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},v=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},1483:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});var n=r(5155);r(2115);var s=r(1362);function a(e){let{children:t,...r}=e;return(0,n.jsx)(s.N,{...r,children:t})}},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},5609:(e,t,r)=>{"use strict";r.d(t,{Navigation:()=>A});var n=r(5155),s=r(2115),a=r(6874),i=r.n(a),o=r(8999),l=r(6408),c=r(869),d=r(2885),h=r(7494),u=r(845),m=r(7351),p=r(1508);class f extends s.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,m.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function v(e){let{children:t,isPresent:r,anchorX:a}=e,i=(0,s.useId)(),o=(0,s.useRef)(null),l=(0,s.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:c}=(0,s.useContext)(p.Q);return(0,s.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:s,right:d}=l.current;if(r||!o.current||!e||!t)return;o.current.dataset.motionPopId=i;let h=document.createElement("style");return c&&(h.nonce=c),document.head.appendChild(h),h.sheet&&h.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===a?"left: ".concat(s):"right: ".concat(d),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.contains(h)&&document.head.removeChild(h)}},[r]),(0,n.jsx)(f,{isPresent:r,childRef:o,sizeRef:l,children:s.cloneElement(t,{ref:o})})}let g=e=>{let{children:t,initial:r,isPresent:a,onExitComplete:i,custom:o,presenceAffectsLayout:l,mode:c,anchorX:h}=e,m=(0,d.M)(b),p=(0,s.useId)(),f=!0,g=(0,s.useMemo)(()=>(f=!1,{id:p,initial:r,isPresent:a,custom:o,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;i&&i()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[a,m,i]);return l&&f&&(g={...g}),(0,s.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[a]),s.useEffect(()=>{a||m.size||!i||i()},[a]),"popLayout"===c&&(t=(0,n.jsx)(v,{isPresent:a,anchorX:h,children:t})),(0,n.jsx)(u.t.Provider,{value:g,children:t})};function b(){return new Map}var x=r(2082);let y=e=>e.key||"";function k(e){let t=[];return s.Children.forEach(e,e=>{(0,s.isValidElement)(e)&&t.push(e)}),t}let w=e=>{let{children:t,custom:r,initial:a=!0,onExitComplete:i,presenceAffectsLayout:o=!0,mode:l="sync",propagate:u=!1,anchorX:m="left"}=e,[p,f]=(0,x.xQ)(u),v=(0,s.useMemo)(()=>k(t),[t]),b=u&&!p?[]:v.map(y),w=(0,s.useRef)(!0),j=(0,s.useRef)(v),N=(0,d.M)(()=>new Map),[E,C]=(0,s.useState)(v),[P,S]=(0,s.useState)(v);(0,h.E)(()=>{w.current=!1,j.current=v;for(let e=0;e<P.length;e++){let t=y(P[e]);b.includes(t)?N.delete(t):!0!==N.get(t)&&N.set(t,!1)}},[P,b.length,b.join("-")]);let M=[];if(v!==E){let e=[...v];for(let t=0;t<P.length;t++){let r=P[t],n=y(r);b.includes(n)||(e.splice(t,0,r),M.push(r))}return"wait"===l&&M.length&&(e=M),S(k(e)),C(v),null}let{forceRender:_}=(0,s.useContext)(c.L);return(0,n.jsx)(n.Fragment,{children:P.map(e=>{let t=y(e),s=(!u||!!p)&&(v===P||b.includes(t));return(0,n.jsx)(g,{isPresent:s,initial:(!w.current||!!a)&&void 0,custom:r,presenceAffectsLayout:o,mode:l,onExitComplete:s?void 0:()=>{if(!N.has(t))return;N.set(t,!0);let e=!0;N.forEach(t=>{t||(e=!1)}),e&&(null==_||_(),S(j.current),u&&(null==f||f()),i&&i())},anchorX:m,children:e},t)})})};var j=r(1362),N=r(285),E=r(9946);let C=(0,E.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),P=(0,E.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),S=(0,E.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),M=(0,E.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var _=r(9434);let T=[{href:"/",label:"Home"},{href:"/about",label:"About"},{href:"/projects",label:"Projects"},{href:"/resume",label:"Resume"},{href:"/contact",label:"Contact"}];function A(){let[e,t]=(0,s.useState)(!1),[r,a]=(0,s.useState)(!1),c=(0,o.usePathname)(),{theme:d,setTheme:h}=(0,j.D)();return((0,s.useEffect)(()=>{a(!0)},[]),r)?(0,n.jsx)(l.P.nav,{initial:{y:-100},animate:{y:0},transition:{duration:.5},className:"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,n.jsx)(i(),{href:"/",className:"flex items-center space-x-2",children:(0,n.jsx)(l.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"text-xl font-bold gradient-text",children:"JK"})}),(0,n.jsx)("div",{className:"hidden md:flex items-center space-x-8",children:T.map(e=>(0,n.jsxs)(i(),{href:e.href,className:(0,_.cn)("relative px-3 py-2 text-sm font-medium transition-colors hover:text-primary",c===e.href?"text-primary":"text-muted-foreground"),children:[e.label,c===e.href&&(0,n.jsx)(l.P.div,{layoutId:"activeTab",className:"absolute inset-x-0 -bottom-px h-px bg-primary",initial:!1,transition:{type:"spring",stiffness:500,damping:30}})]},e.href))}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)(N.$,{variant:"ghost",size:"icon",onClick:()=>{h("dark"===d?"light":"dark")},className:"w-9 h-9",children:"dark"===d?(0,n.jsx)(C,{className:"h-4 w-4"}):(0,n.jsx)(P,{className:"h-4 w-4"})}),(0,n.jsx)(N.$,{variant:"ghost",size:"icon",className:"md:hidden w-9 h-9",onClick:()=>t(!e),children:e?(0,n.jsx)(S,{className:"h-4 w-4"}):(0,n.jsx)(M,{className:"h-4 w-4"})})]})]}),(0,n.jsx)(w,{children:e&&(0,n.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.2},className:"md:hidden border-t border-border",children:(0,n.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:T.map(e=>(0,n.jsx)(i(),{href:e.href,onClick:()=>t(!1),className:(0,_.cn)("block px-3 py-2 text-base font-medium rounded-md transition-colors",c===e.href?"text-primary bg-primary/10":"text-muted-foreground hover:text-primary hover:bg-primary/5"),children:e.label},e.href))})})})]})}):null}},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var n=r(2596),s=r(9688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[360,154,874,441,684,358],()=>t(1175)),_N_E=e.O()}]);