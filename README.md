# <PERSON><PERSON> - Portfolio Website

A modern, professional software developer portfolio website built with Next.js, Tailwind CSS, and Framer Motion. Features 3D animations, dark mode, and smooth scroll effects to showcase a cutting-edge, tech-forward personal brand.

## 🚀 Features

- **Modern Design**: Clean, professional layout with gradient accents
- **3D Animations**: Interactive 3D background using Three.js and React Three Fiber
- **Smooth Animations**: Page transitions and scroll-based animations with Framer Motion
- **Dark Mode**: Toggle between light and dark themes
- **Responsive Design**: Fully responsive across all devices
- **SEO Optimized**: Proper meta tags and structured data
- **Performance Optimized**: Fast loading with Next.js 14 and optimized assets

## 🛠️ Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Animations**: Framer Motion
- **3D Graphics**: @react-three/fiber + @react-three/drei
- **Icons**: Lucide React
- **Theme**: next-themes
- **TypeScript**: Full type safety

## 📄 Pages

1. **Home (/)** - Hero section with 3D background and call-to-action
2. **About (/about)** - Personal bio, education, skills, and interests
3. **Projects (/projects)** - Showcase of development projects with tech stacks
4. **Resume (/resume)** - Downloadable resume with detailed experience
5. **Contact (/contact)** - Contact form and social media links

## 🎨 Design Features

- **Hero Section**: Animated gradient text with 3D floating particles
- **Navigation**: Fixed header with smooth scroll indicators
- **Scroll Animations**: Elements animate in as you scroll
- **3D Background**: Interactive particle system and floating geometric shapes
- **Custom Scrollbar**: Styled scrollbar matching the theme
- **Gradient Effects**: Beautiful gradient backgrounds and text effects

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone https://github.com/janvi-kalwani/portfolio.git
cd portfolio
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
src/
├── app/                    # Next.js app router pages
│   ├── about/             # About page
│   ├── contact/           # Contact page
│   ├── projects/          # Projects page
│   ├── resume/            # Resume page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── ui/               # shadcn/ui components
│   ├── hero-section.tsx  # Hero section component
│   ├── navigation.tsx    # Navigation component
│   ├── theme-provider.tsx # Theme provider
│   └── three-background.tsx # 3D background
└── lib/
    └── utils.ts          # Utility functions
```

## 🎯 Customization

### Personal Information
Update the following files with your information:
- `src/app/layout.tsx` - Meta tags and SEO information
- `src/components/hero-section.tsx` - Name, title, and social links
- `src/app/about/page.tsx` - Bio, education, and skills
- `src/app/projects/page.tsx` - Project details and links
- `src/app/resume/page.tsx` - Experience and contact information
- `src/app/contact/page.tsx` - Contact information

### Styling
- Modify `src/app/globals.css` for custom styles
- Update Tailwind configuration in `tailwind.config.js`
- Customize color scheme in the CSS variables

### 3D Effects
- Modify `src/components/three-background.tsx` for different 3D effects
- Adjust particle count, colors, and animations

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Deploy with one click

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions, issues, and feature requests are welcome! Feel free to check the [issues page](https://github.com/janvi-kalwani/portfolio/issues).

## 📧 Contact

Janvi Kalwani - [<EMAIL>](mailto:<EMAIL>)

Project Link: [https://github.com/janvi-kalwani/portfolio](https://github.com/janvi-kalwani/portfolio)
