(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{484:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>h});var i=t(5155),r=t(6408),n=t(6695),s=t(6126),l=t(9946);let o=(0,l.A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),d=(0,l.A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]),c=(0,l.A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),p=(0,l.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),x=["C++","Java","Python","JavaScript","TypeScript","React","Node.js","Next.js","Express.js","MongoDB","SQL","PostgreSQL","HTML","CSS","Tailwind CSS","Git","GitHub","Selenium","REST APIs"],u=[{degree:"Master of Computer Applications (MCA)",institution:"Amity University Raipur",year:"2022-2024",icon:o},{degree:"Bachelor of Computer Applications (BCA)",institution:"ABVV Bilaspur",year:"2019-2022",icon:o}],m=[{title:"Web Development",description:"Building responsive and interactive web applications",icon:d},{title:"AI & Machine Learning",description:"Exploring artificial intelligence and machine learning applications",icon:c},{title:"Performance Optimization",description:"Creating fast, efficient, and scalable applications",icon:p}];function h(){return(0,i.jsx)("div",{className:"pt-16 min-h-screen",children:(0,i.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-12",children:[(0,i.jsxs)("h1",{className:"text-4xl sm:text-5xl font-bold mb-4",children:["About ",(0,i.jsx)("span",{className:"gradient-text",children:"Me"})]}),(0,i.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Passionate about creating innovative solutions and building the future of technology"})]}),(0,i.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"mb-12",children:(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{children:"My Journey"})}),(0,i.jsxs)(n.Wu,{className:"prose prose-neutral dark:prose-invert max-w-none",children:[(0,i.jsx)("p",{className:"text-lg leading-relaxed",children:"I'm an aspiring software developer with a Master's degree in Computer Applications from Amity University Raipur. My journey in technology began with a curiosity about how things work and evolved into a passion for creating solutions that make a difference."}),(0,i.jsx)("p",{className:"text-lg leading-relaxed",children:"With expertise in modern web technologies like React, Node.js, and Python, I focus on building secure, scalable applications. I'm particularly interested in the intersection of web development and artificial intelligence, always looking for ways to leverage cutting-edge technologies to solve real-world problems."})]})]})}),(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"mb-12",children:[(0,i.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Education"}),(0,i.jsx)("div",{className:"grid gap-6 md:grid-cols-2",children:u.map((e,a)=>(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(e.icon,{className:"h-6 w-6 text-primary"}),(0,i.jsx)(n.ZB,{className:"text-lg",children:e.degree})]})}),(0,i.jsxs)(n.Wu,{children:[(0,i.jsx)("p",{className:"text-muted-foreground",children:e.institution}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:e.year})]})]},a))})]}),(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3},className:"mb-12",children:[(0,i.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Skills & Technologies"}),(0,i.jsx)(n.Zp,{children:(0,i.jsx)(n.Wu,{className:"pt-6",children:(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:x.map((e,a)=>(0,i.jsx)(r.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.3,delay:.05*a},children:(0,i.jsx)(s.E,{variant:"secondary",className:"text-sm",children:e})},e))})})})]}),(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.4},children:[(0,i.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Areas of Interest"}),(0,i.jsx)("div",{className:"grid gap-6 md:grid-cols-3",children:m.map((e,a)=>(0,i.jsxs)(n.Zp,{className:"text-center",children:[(0,i.jsxs)(n.aR,{children:[(0,i.jsx)("div",{className:"flex justify-center mb-2",children:(0,i.jsx)(e.icon,{className:"h-8 w-8 text-primary"})}),(0,i.jsx)(n.ZB,{className:"text-lg",children:e.title})]}),(0,i.jsx)(n.Wu,{children:(0,i.jsx)("p",{className:"text-muted-foreground",children:e.description})})]},a))})]})]})})})}},4818:(e,a,t)=>{Promise.resolve().then(t.bind(t,484))},6126:(e,a,t)=>{"use strict";t.d(a,{E:()=>o});var i=t(5155);t(2115);var r=t(4624),n=t(2085),s=t(9434);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:a,variant:t,asChild:n=!1,...o}=e,d=n?r.DX:"span";return(0,i.jsx)(d,{"data-slot":"badge",className:(0,s.cn)(l({variant:t}),a),...o})}},6695:(e,a,t)=>{"use strict";t.d(a,{Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>s});var i=t(5155);t(2115);var r=t(9434);function n(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function s(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function l(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function o(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}},9434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>n});var i=t(2596),r=t(9688);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,i.$)(a))}}},e=>{var a=a=>e(e.s=a);e.O(0,[154,441,684,358],()=>a(4818)),_N_E=e.O()}]);