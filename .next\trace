[{"name": "generate-buildid", "duration": 352, "timestamp": 294140767904, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748875214057, "traceId": "febc56075bf779d1"}, {"name": "load-custom-routes", "duration": 384, "timestamp": 294140768397, "id": 5, "parentId": 1, "tags": {}, "startTime": 1748875214058, "traceId": "febc56075bf779d1"}, {"name": "create-dist-dir", "duration": 515, "timestamp": 294140860627, "id": 6, "parentId": 1, "tags": {}, "startTime": 1748875214150, "traceId": "febc56075bf779d1"}, {"name": "create-pages-mapping", "duration": 251, "timestamp": 294140878557, "id": 7, "parentId": 1, "tags": {}, "startTime": 1748875214168, "traceId": "febc56075bf779d1"}, {"name": "collect-app-paths", "duration": 2675, "timestamp": 294140878854, "id": 8, "parentId": 1, "tags": {}, "startTime": 1748875214168, "traceId": "febc56075bf779d1"}, {"name": "create-app-mapping", "duration": 3436, "timestamp": 294140881572, "id": 9, "parentId": 1, "tags": {}, "startTime": 1748875214171, "traceId": "febc56075bf779d1"}, {"name": "public-dir-conflict-check", "duration": 1307, "timestamp": 294140885618, "id": 10, "parentId": 1, "tags": {}, "startTime": 1748875214175, "traceId": "febc56075bf779d1"}, {"name": "generate-routes-manifest", "duration": 3112, "timestamp": 294140887220, "id": 11, "parentId": 1, "tags": {}, "startTime": 1748875214177, "traceId": "febc56075bf779d1"}, {"name": "create-entrypoints", "duration": 22726, "timestamp": 294142187417, "id": 15, "parentId": 13, "tags": {}, "startTime": 1748875215479, "traceId": "febc56075bf779d1"}, {"name": "generate-webpack-config", "duration": 560398, "timestamp": 294142210392, "id": 16, "parentId": 14, "tags": {}, "startTime": 1748875215502, "traceId": "febc56075bf779d1"}, {"name": "next-trace-entrypoint-plugin", "duration": 3286, "timestamp": 294142951320, "id": 18, "parentId": 17, "tags": {}, "startTime": 1748875216243, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 477565, "timestamp": 294142964322, "id": 23, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1748875216256, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 521204, "timestamp": 294142964168, "id": 22, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1748875216256, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 566596, "timestamp": 294142964480, "id": 29, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1748875216256, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 605120, "timestamp": 294142964107, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=D%3A%5Cportfolio%5Cjanvi-portfolio%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748875216256, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 721057, "timestamp": 294142963233, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=D%3A%5Cportfolio%5Cjanvi-portfolio%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748875216255, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 719955, "timestamp": 294142964363, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fcontact%2Fpage&name=app%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cjanvi-portfolio%5Csrc%5Capp&appPaths=%2Fcontact%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748875216256, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 719949, "timestamp": 294142964384, "id": 25, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fabout%2Fpage&name=app%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cjanvi-portfolio%5Csrc%5Capp&appPaths=%2Fabout%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748875216256, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 719945, "timestamp": 294142964422, "id": 27, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fprojects%2Fpage&name=app%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fprojects%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cjanvi-portfolio%5Csrc%5Capp&appPaths=%2Fprojects%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748875216256, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 719972, "timestamp": 294142964443, "id": 28, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fresume%2Fpage&name=app%2Fresume%2Fpage&pagePath=private-next-app-dir%2Fresume%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cjanvi-portfolio%5Csrc%5Capp&appPaths=%2Fresume%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748875216256, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 720033, "timestamp": 294142964402, "id": 26, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cjanvi-portfolio%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748875216256, "traceId": "febc56075bf779d1"}, {"name": "make", "duration": 1803562, "timestamp": 294142962736, "id": 19, "parentId": 17, "tags": {}, "startTime": 1748875216254, "traceId": "febc56075bf779d1"}, {"name": "get-entries", "duration": 4034, "timestamp": 294144768033, "id": 65, "parentId": 64, "tags": {}, "startTime": 1748875218060, "traceId": "febc56075bf779d1"}, {"name": "node-file-trace-plugin", "duration": 108939, "timestamp": 294144775688, "id": 66, "parentId": 64, "tags": {"traceEntryCount": "16"}, "startTime": 1748875218067, "traceId": "febc56075bf779d1"}, {"name": "collect-traced-files", "duration": 679, "timestamp": 294144884642, "id": 67, "parentId": 64, "tags": {}, "startTime": 1748875218176, "traceId": "febc56075bf779d1"}, {"name": "finish-modules", "duration": 117584, "timestamp": 294144767765, "id": 64, "parentId": 18, "tags": {}, "startTime": 1748875218059, "traceId": "febc56075bf779d1"}, {"name": "chunk-graph", "duration": 46388, "timestamp": 294145045325, "id": 69, "parentId": 68, "tags": {}, "startTime": 1748875218337, "traceId": "febc56075bf779d1"}, {"name": "optimize-modules", "duration": 93, "timestamp": 294145092069, "id": 71, "parentId": 68, "tags": {}, "startTime": 1748875218384, "traceId": "febc56075bf779d1"}, {"name": "optimize-chunks", "duration": 55603, "timestamp": 294145092398, "id": 72, "parentId": 68, "tags": {}, "startTime": 1748875218384, "traceId": "febc56075bf779d1"}, {"name": "optimize-tree", "duration": 241, "timestamp": 294145148166, "id": 73, "parentId": 68, "tags": {}, "startTime": 1748875218440, "traceId": "febc56075bf779d1"}, {"name": "optimize-chunk-modules", "duration": 49460, "timestamp": 294145148600, "id": 74, "parentId": 68, "tags": {}, "startTime": 1748875218440, "traceId": "febc56075bf779d1"}, {"name": "optimize", "duration": 106292, "timestamp": 294145091924, "id": 70, "parentId": 68, "tags": {}, "startTime": 1748875218384, "traceId": "febc56075bf779d1"}, {"name": "module-hash", "duration": 50170, "timestamp": 294145238934, "id": 75, "parentId": 68, "tags": {}, "startTime": 1748875218531, "traceId": "febc56075bf779d1"}, {"name": "code-generation", "duration": 6961, "timestamp": 294145289215, "id": 76, "parentId": 68, "tags": {}, "startTime": 1748875218581, "traceId": "febc56075bf779d1"}, {"name": "hash", "duration": 15281, "timestamp": 294145305594, "id": 77, "parentId": 68, "tags": {}, "startTime": 1748875218597, "traceId": "febc56075bf779d1"}, {"name": "code-generation-jobs", "duration": 416, "timestamp": 294145320872, "id": 78, "parentId": 68, "tags": {}, "startTime": 1748875218613, "traceId": "febc56075bf779d1"}, {"name": "module-assets", "duration": 726, "timestamp": 294145321213, "id": 79, "parentId": 68, "tags": {}, "startTime": 1748875218613, "traceId": "febc56075bf779d1"}, {"name": "create-chunk-assets", "duration": 2322, "timestamp": 294145321962, "id": 80, "parentId": 68, "tags": {}, "startTime": 1748875218614, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 534, "timestamp": 294145338909, "id": 82, "parentId": 81, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 328, "timestamp": 294145339126, "id": 83, "parentId": 81, "tags": {"name": "../app/favicon.ico/route.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 312, "timestamp": 294145339144, "id": 84, "parentId": 81, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 268, "timestamp": 294145339190, "id": 85, "parentId": 81, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 251, "timestamp": 294145339208, "id": 86, "parentId": 81, "tags": {"name": "../app/contact/page.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 240, "timestamp": 294145339220, "id": 87, "parentId": 81, "tags": {"name": "../app/about/page.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 231, "timestamp": 294145339230, "id": 88, "parentId": 81, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 224, "timestamp": 294145339239, "id": 89, "parentId": 81, "tags": {"name": "../app/projects/page.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 216, "timestamp": 294145339247, "id": 90, "parentId": 81, "tags": {"name": "../app/resume/page.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 208, "timestamp": 294145339257, "id": 91, "parentId": 81, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 103, "timestamp": 294145339363, "id": 92, "parentId": 81, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 87, "timestamp": 294145339380, "id": 93, "parentId": 81, "tags": {"name": "447.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 77, "timestamp": 294145339391, "id": 94, "parentId": 81, "tags": {"name": "70.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 70, "timestamp": 294145339400, "id": 95, "parentId": 81, "tags": {"name": "658.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 62, "timestamp": 294145339410, "id": 96, "parentId": 81, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 46, "timestamp": 294145339427, "id": 97, "parentId": 81, "tags": {"name": "235.js", "cache": "HIT"}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "minify-webpack-plugin-optimize", "duration": 9338, "timestamp": 294145330149, "id": 81, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1748875218622, "traceId": "febc56075bf779d1"}, {"name": "css-minimizer-plugin", "duration": 196, "timestamp": 294145339666, "id": 98, "parentId": 17, "tags": {}, "startTime": 1748875218631, "traceId": "febc56075bf779d1"}, {"name": "create-trace-assets", "duration": 3881, "timestamp": 294145340149, "id": 99, "parentId": 18, "tags": {}, "startTime": 1748875218632, "traceId": "febc56075bf779d1"}, {"name": "seal", "duration": 400349, "timestamp": 294144964249, "id": 68, "parentId": 17, "tags": {}, "startTime": 1748875218256, "traceId": "febc56075bf779d1"}, {"name": "webpack-compilation", "duration": 2451797, "timestamp": 294142948559, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1748875216240, "traceId": "febc56075bf779d1"}, {"name": "emit", "duration": 24235, "timestamp": 294145401450, "id": 100, "parentId": 14, "tags": {}, "startTime": 1748875218693, "traceId": "febc56075bf779d1"}, {"name": "webpack-close", "duration": 3043, "timestamp": 294145432593, "id": 101, "parentId": 14, "tags": {"name": "server"}, "startTime": 1748875218724, "traceId": "febc56075bf779d1"}, {"name": "webpack-generate-error-stats", "duration": 8567, "timestamp": 294145435829, "id": 102, "parentId": 101, "tags": {}, "startTime": 1748875218728, "traceId": "febc56075bf779d1"}, {"name": "run-webpack-compiler", "duration": 3257912, "timestamp": 294142187408, "id": 14, "parentId": 13, "tags": {}, "startTime": 1748875215479, "traceId": "febc56075bf779d1"}, {"name": "format-webpack-messages", "duration": 304, "timestamp": 294145445359, "id": 103, "parentId": 13, "tags": {}, "startTime": 1748875218737, "traceId": "febc56075bf779d1"}, {"name": "worker-main-server", "duration": 3259120, "timestamp": 294142187010, "id": 13, "parentId": 1, "tags": {}, "startTime": 1748875215479, "traceId": "febc56075bf779d1"}, {"name": "create-entrypoints", "duration": 27192, "timestamp": 294146969431, "id": 106, "parentId": 104, "tags": {}, "startTime": 1748875220261, "traceId": "febc56075bf779d1"}, {"name": "generate-webpack-config", "duration": 616992, "timestamp": 294146996851, "id": 107, "parentId": 105, "tags": {}, "startTime": 1748875220289, "traceId": "febc56075bf779d1"}, {"name": "make", "duration": 1516, "timestamp": 294147831388, "id": 109, "parentId": 108, "tags": {}, "startTime": 1748875221123, "traceId": "febc56075bf779d1"}, {"name": "chunk-graph", "duration": 1436, "timestamp": 294147839105, "id": 111, "parentId": 110, "tags": {}, "startTime": 1748875221131, "traceId": "febc56075bf779d1"}, {"name": "optimize-modules", "duration": 75, "timestamp": 294147840841, "id": 113, "parentId": 110, "tags": {}, "startTime": 1748875221133, "traceId": "febc56075bf779d1"}, {"name": "optimize-chunks", "duration": 2134, "timestamp": 294147841120, "id": 114, "parentId": 110, "tags": {}, "startTime": 1748875221133, "traceId": "febc56075bf779d1"}, {"name": "optimize-tree", "duration": 269, "timestamp": 294147843437, "id": 115, "parentId": 110, "tags": {}, "startTime": 1748875221135, "traceId": "febc56075bf779d1"}, {"name": "optimize-chunk-modules", "duration": 1104, "timestamp": 294147844180, "id": 116, "parentId": 110, "tags": {}, "startTime": 1748875221136, "traceId": "febc56075bf779d1"}, {"name": "optimize", "duration": 4933, "timestamp": 294147840717, "id": 112, "parentId": 110, "tags": {}, "startTime": 1748875221133, "traceId": "febc56075bf779d1"}, {"name": "module-hash", "duration": 178, "timestamp": 294147847872, "id": 117, "parentId": 110, "tags": {}, "startTime": 1748875221140, "traceId": "febc56075bf779d1"}, {"name": "code-generation", "duration": 400, "timestamp": 294147848131, "id": 118, "parentId": 110, "tags": {}, "startTime": 1748875221140, "traceId": "febc56075bf779d1"}, {"name": "hash", "duration": 809, "timestamp": 294147849197, "id": 119, "parentId": 110, "tags": {}, "startTime": 1748875221141, "traceId": "febc56075bf779d1"}, {"name": "code-generation-jobs", "duration": 256, "timestamp": 294147850002, "id": 120, "parentId": 110, "tags": {}, "startTime": 1748875221142, "traceId": "febc56075bf779d1"}, {"name": "module-assets", "duration": 265, "timestamp": 294147850199, "id": 121, "parentId": 110, "tags": {}, "startTime": 1748875221142, "traceId": "febc56075bf779d1"}, {"name": "create-chunk-assets", "duration": 364, "timestamp": 294147850487, "id": 122, "parentId": 110, "tags": {}, "startTime": 1748875221142, "traceId": "febc56075bf779d1"}, {"name": "minify-js", "duration": 412, "timestamp": 294147869589, "id": 124, "parentId": 123, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1748875221161, "traceId": "febc56075bf779d1"}, {"name": "minify-webpack-plugin-optimize", "duration": 5326, "timestamp": 294147864688, "id": 123, "parentId": 108, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1748875221157, "traceId": "febc56075bf779d1"}, {"name": "css-minimizer-plugin", "duration": 221, "timestamp": 294147870163, "id": 125, "parentId": 108, "tags": {}, "startTime": 1748875221162, "traceId": "febc56075bf779d1"}, {"name": "seal", "duration": 38658, "timestamp": 294147837629, "id": 110, "parentId": 108, "tags": {}, "startTime": 1748875221130, "traceId": "febc56075bf779d1"}, {"name": "webpack-compilation", "duration": 59686, "timestamp": 294147817238, "id": 108, "parentId": 105, "tags": {"name": "edge-server"}, "startTime": 1748875221109, "traceId": "febc56075bf779d1"}, {"name": "emit", "duration": 5091, "timestamp": 294147877573, "id": 126, "parentId": 105, "tags": {}, "startTime": 1748875221169, "traceId": "febc56075bf779d1"}, {"name": "webpack-close", "duration": 1284, "timestamp": 294147884793, "id": 127, "parentId": 105, "tags": {"name": "edge-server"}, "startTime": 1748875221177, "traceId": "febc56075bf779d1"}, {"name": "webpack-generate-error-stats", "duration": 4454, "timestamp": 294147886156, "id": 128, "parentId": 127, "tags": {}, "startTime": 1748875221178, "traceId": "febc56075bf779d1"}, {"name": "run-webpack-compiler", "duration": 921298, "timestamp": 294146969421, "id": 105, "parentId": 104, "tags": {}, "startTime": 1748875220261, "traceId": "febc56075bf779d1"}, {"name": "format-webpack-messages", "duration": 135, "timestamp": 294147890729, "id": 129, "parentId": 104, "tags": {}, "startTime": 1748875221183, "traceId": "febc56075bf779d1"}, {"name": "worker-main-edge-server", "duration": 922094, "timestamp": 294146968970, "id": 104, "parentId": 1, "tags": {}, "startTime": 1748875220261, "traceId": "febc56075bf779d1"}, {"name": "create-entrypoints", "duration": 28459, "timestamp": 294149288937, "id": 132, "parentId": 130, "tags": {}, "startTime": 1748875222580, "traceId": "febc56075bf779d1"}, {"name": "generate-webpack-config", "duration": 639007, "timestamp": 294149317615, "id": 133, "parentId": 131, "tags": {}, "startTime": 1748875222608, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 555513, "timestamp": 294150170158, "id": 139, "parentId": 135, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1748875223461, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 726457, "timestamp": 294150170346, "id": 140, "parentId": 135, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1748875223461, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 726440, "timestamp": 294150170396, "id": 142, "parentId": 135, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1748875223461, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 745230, "timestamp": 294150170375, "id": 141, "parentId": 135, "tags": {"request": "D:\\portfolio\\janvi-portfolio\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1748875223461, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 811096, "timestamp": 294150170113, "id": 138, "parentId": 135, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1748875223461, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 811217, "timestamp": 294150170059, "id": 137, "parentId": 135, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1748875223461, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 1167618, "timestamp": 294150169173, "id": 136, "parentId": 135, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1748875223460, "traceId": "febc56075bf779d1"}, {"name": "add-entry", "duration": 1389052, "timestamp": 294150170415, "id": 143, "parentId": 135, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cjanvi-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false!"}, "startTime": 1748875223461, "traceId": "febc56075bf779d1"}]