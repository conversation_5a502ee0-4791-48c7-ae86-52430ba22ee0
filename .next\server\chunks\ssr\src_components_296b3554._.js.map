{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/janvi-portfolio/src/components/three-background.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useRef, useMemo } from \"react\";\nimport { Canvas, useFrame } from \"@react-three/fiber\";\nimport { Points, PointMaterial } from \"@react-three/drei\";\nimport * as THREE from \"three\";\n\nfunction AnimatedSphere() {\n  const ref = useRef<THREE.Points>(null);\n  \n  // Generate random points in a sphere\n  const [positions, colors] = useMemo(() => {\n    const positions = new Float32Array(5000 * 3);\n    const colors = new Float32Array(5000 * 3);\n    \n    for (let i = 0; i < 5000; i++) {\n      // Random position in sphere\n      const radius = Math.random() * 2;\n      const theta = Math.random() * Math.PI * 2;\n      const phi = Math.acos(2 * Math.random() - 1);\n      \n      positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);\n      positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);\n      positions[i * 3 + 2] = radius * Math.cos(phi);\n      \n      // Random colors with blue/purple theme\n      colors[i * 3] = Math.random() * 0.5 + 0.5; // R\n      colors[i * 3 + 1] = Math.random() * 0.3 + 0.4; // G\n      colors[i * 3 + 2] = Math.random() * 0.3 + 0.7; // B\n    }\n    \n    return [positions, colors];\n  }, []);\n\n  useFrame((state) => {\n    if (ref.current) {\n      ref.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.1;\n      ref.current.rotation.y = state.clock.elapsedTime * 0.05;\n      ref.current.rotation.z = Math.sin(state.clock.elapsedTime * 0.1) * 0.05;\n    }\n  });\n\n  return (\n    <Points ref={ref} positions={positions} stride={3} frustumCulled={false}>\n      <PointMaterial\n        transparent\n        vertexColors\n        size={0.015}\n        sizeAttenuation={true}\n        depthWrite={false}\n        blending={THREE.AdditiveBlending}\n      />\n    </Points>\n  );\n}\n\nfunction FloatingCubes() {\n  const meshRef = useRef<THREE.Group>(null);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.2) * 0.1;\n      meshRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n    }\n  });\n\n  const cubes = useMemo(() => {\n    const temp = [];\n    for (let i = 0; i < 50; i++) {\n      temp.push({\n        position: [\n          (Math.random() - 0.5) * 10,\n          (Math.random() - 0.5) * 10,\n          (Math.random() - 0.5) * 10,\n        ] as [number, number, number],\n        rotation: [\n          Math.random() * Math.PI,\n          Math.random() * Math.PI,\n          Math.random() * Math.PI,\n        ] as [number, number, number],\n        scale: Math.random() * 0.1 + 0.05,\n      });\n    }\n    return temp;\n  }, []);\n\n  return (\n    <group ref={meshRef}>\n      {cubes.map((cube, index) => (\n        <mesh key={index} position={cube.position} rotation={cube.rotation} scale={cube.scale}>\n          <boxGeometry args={[1, 1, 1]} />\n          <meshBasicMaterial\n            color={new THREE.Color().setHSL(0.6 + Math.random() * 0.2, 0.7, 0.6)}\n            transparent\n            opacity={0.6}\n            wireframe\n          />\n        </mesh>\n      ))}\n    </group>\n  );\n}\n\nexport function ThreeBackground() {\n  return (\n    <div className=\"absolute inset-0 -z-10\">\n      <Canvas\n        camera={{ position: [0, 0, 5], fov: 75 }}\n        style={{ background: \"transparent\" }}\n      >\n        <ambientLight intensity={0.5} />\n        <pointLight position={[10, 10, 10]} />\n        <AnimatedSphere />\n        <FloatingCubes />\n      </Canvas>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AALA;;;;;;AAOA,SAAS;IACP,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IAEjC,qCAAqC;IACrC,MAAM,CAAC,WAAW,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAClC,MAAM,YAAY,IAAI,aAAa,OAAO;QAC1C,MAAM,SAAS,IAAI,aAAa,OAAO;QAEvC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC7B,4BAA4B;YAC5B,MAAM,SAAS,KAAK,MAAM,KAAK;YAC/B,MAAM,QAAQ,KAAK,MAAM,KAAK,KAAK,EAAE,GAAG;YACxC,MAAM,MAAM,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM,KAAK;YAE1C,SAAS,CAAC,IAAI,EAAE,GAAG,SAAS,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC;YACrD,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,SAAS,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC;YACzD,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,SAAS,KAAK,GAAG,CAAC;YAEzC,uCAAuC;YACvC,MAAM,CAAC,IAAI,EAAE,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;YAC/C,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;YACnD,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;QACrD;QAEA,OAAO;YAAC;YAAW;SAAO;IAC5B,GAAG,EAAE;IAEL,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,IAAI,OAAO,EAAE;YACf,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;YACnE,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACnD,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QACrE;IACF;IAEA,qBACE,8OAAC,0JAAA,CAAA,SAAM;QAAC,KAAK;QAAK,WAAW;QAAW,QAAQ;QAAG,eAAe;kBAChE,cAAA,8OAAC,iKAAA,CAAA,gBAAa;YACZ,WAAW;YACX,YAAY;YACZ,MAAM;YACN,iBAAiB;YACjB,YAAY;YACZ,UAAU,+IAAA,CAAA,mBAAsB;;;;;;;;;;;AAIxC;AAEA,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAEpC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;YACvE,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QACzD;IACF;IAEA,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,OAAO,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YAC3B,KAAK,IAAI,CAAC;gBACR,UAAU;oBACR,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;iBACzB;gBACD,UAAU;oBACR,KAAK,MAAM,KAAK,KAAK,EAAE;oBACvB,KAAK,MAAM,KAAK,KAAK,EAAE;oBACvB,KAAK,MAAM,KAAK,KAAK,EAAE;iBACxB;gBACD,OAAO,KAAK,MAAM,KAAK,MAAM;YAC/B;QACF;QACA,OAAO;IACT,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAM,KAAK;kBACT,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAAiB,UAAU,KAAK,QAAQ;gBAAE,UAAU,KAAK,QAAQ;gBAAE,OAAO,KAAK,KAAK;;kCACnF,8OAAC;wBAAY,MAAM;4BAAC;4BAAG;4BAAG;yBAAE;;;;;;kCAC5B,8OAAC;wBACC,OAAO,IAAI,+IAAA,CAAA,QAAW,GAAG,MAAM,CAAC,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK;wBAChE,WAAW;wBACX,SAAS;wBACT,SAAS;;;;;;;eANF;;;;;;;;;;AAYnB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,mMAAA,CAAA,SAAM;YACL,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,KAAK;YAAG;YACvC,OAAO;gBAAE,YAAY;YAAc;;8BAEnC,8OAAC;oBAAa,WAAW;;;;;;8BACzB,8OAAC;oBAAW,UAAU;wBAAC;wBAAI;wBAAI;qBAAG;;;;;;8BAClC,8OAAC;;;;;8BACD,8OAAC;;;;;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/janvi-portfolio/src/components/hero-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { ArrowDown, Download, Github, Linkedin, Mail } from \"lucide-react\";\nimport { ThreeBackground } from \"./three-background\";\nimport Link from \"next/link\";\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      delayChildren: 0.3,\n      staggerChildren: 0.2,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { y: 20, opacity: 0 },\n  visible: {\n    y: 0,\n    opacity: 1,\n    transition: {\n      duration: 0.5,\n    },\n  },\n};\n\nexport function HeroSection() {\n  const scrollToNext = () => {\n    const nextSection = document.getElementById(\"about\");\n    nextSection?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      <ThreeBackground />\n      \n      {/* Gradient overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-background/90 via-background/70 to-background/90\" />\n      \n      <motion.div\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n        className=\"relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto\"\n      >\n        <motion.div variants={itemVariants} className=\"mb-6\">\n          <h1 className=\"text-4xl sm:text-6xl lg:text-7xl font-bold tracking-tight\">\n            <span className=\"block\">Hi, I'm</span>\n            <span className=\"block gradient-text\">Janvi Kalwani</span>\n          </h1>\n        </motion.div>\n\n        <motion.div variants={itemVariants} className=\"mb-6\">\n          <h2 className=\"text-xl sm:text-2xl lg:text-3xl font-medium text-muted-foreground\">\n            Aspiring Software Developer\n          </h2>\n        </motion.div>\n\n        <motion.div variants={itemVariants} className=\"mb-8\">\n          <p className=\"text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed\">\n            Building secure, scalable web and AI applications with modern technologies.\n            Passionate about creating innovative solutions that make a difference.\n          </p>\n        </motion.div>\n\n        <motion.div variants={itemVariants} className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n          <Link href=\"/projects\">\n            <Button size=\"lg\" className=\"w-full sm:w-auto animate-pulse-glow\">\n              View Projects\n            </Button>\n          </Link>\n          <Link href=\"/resume\">\n            <Button variant=\"outline\" size=\"lg\" className=\"w-full sm:w-auto\">\n              <Download className=\"mr-2 h-4 w-4\" />\n              Download Resume\n            </Button>\n          </Link>\n        </motion.div>\n\n        <motion.div variants={itemVariants} className=\"flex justify-center space-x-6 mb-12\">\n          <Link\n            href=\"https://github.com/janvi-kalwani\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"text-muted-foreground hover:text-primary transition-colors\"\n          >\n            <Github className=\"h-6 w-6\" />\n          </Link>\n          <Link\n            href=\"https://linkedin.com/in/janvi-kalwani\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"text-muted-foreground hover:text-primary transition-colors\"\n          >\n            <Linkedin className=\"h-6 w-6\" />\n          </Link>\n          <Link\n            href=\"mailto:<EMAIL>\"\n            className=\"text-muted-foreground hover:text-primary transition-colors\"\n          >\n            <Mail className=\"h-6 w-6\" />\n          </Link>\n        </motion.div>\n\n        <motion.div\n          variants={itemVariants}\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        >\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={scrollToNext}\n            className=\"animate-bounce\"\n          >\n            <ArrowDown className=\"h-4 w-4\" />\n          </Button>\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,eAAe;YACf,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,GAAG;QAAI,SAAS;IAAE;IAC5B,SAAS;QACP,GAAG;QACH,SAAS;QACT,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;IACd,MAAM,eAAe;QACnB,MAAM,cAAc,SAAS,cAAc,CAAC;QAC5C,aAAa,eAAe;YAAE,UAAU;QAAS;IACnD;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC,yIAAA,CAAA,kBAAe;;;;;0BAGhB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,WAAU;;kCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;kCAC5C,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,8OAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;;;;;;kCAI1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;kCAC5C,cAAA,8OAAC;4BAAG,WAAU;sCAAoE;;;;;;;;;;;kCAKpF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;kCAC5C,cAAA,8OAAC;4BAAE,WAAU;sCAA6E;;;;;;;;;;;kCAM5F,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;8CAAsC;;;;;;;;;;;0CAIpE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAM3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjC", "debugId": null}}]}